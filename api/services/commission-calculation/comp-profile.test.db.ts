import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { CommissionCalculationProfileService } from './comp-profile';
import { prismaClient } from '@/lib/prisma';
import { findContactWithAncestorsCTE } from '@/lib/queries';
import { asyncLocalStorage } from '@/services/logger/appLogger';
import type { ExtAccountInfo } from '@/types';

describe('CommissionCalculationProfileService', () => {
  let service: CommissionCalculationProfileService;

  beforeEach(async () => {
    service = new CommissionCalculationProfileService();
  });

  afterEach(async () => {
    await prismaClient.$transaction([
      prismaClient.contacts_agent_commission_schedule_profiles.deleteMany({}),
      prismaClient.contacts_agent_commission_schedule_profiles_sets.deleteMany(
        {}
      ),
      prismaClient.agent_commission_schedule_profiles.deleteMany({}),
      prismaClient.agent_commission_schedule_profiles_sets.deleteMany({}),
      prismaClient.contact_hierarchy.deleteMany({}),
      prismaClient.contacts.deleteMany({}),
    ]);
  });

  describe('getEffectiveDateRangeCondition', () => {
    it('Given no effective date, should return an empty object', () => {
      const result = service.getEffectiveDateRangeCondition();
      expect(result).toEqual({});
    });

    it('Given an effective date, should return the correct date range condition', () => {
      const effectiveDate = new Date();
      const result = service.getEffectiveDateRangeCondition(effectiveDate);

      expect(result).toEqual({
        OR: [
          {
            start_date: { lte: effectiveDate },
            end_date: { gte: effectiveDate },
          },
          {
            start_date: { lte: effectiveDate },
            end_date: null,
          },
          {
            start_date: null,
            end_date: { gte: effectiveDate },
          },
          {
            start_date: null,
            end_date: null,
          },
        ],
      });
    });
  });

  describe('getCompProfiles', () => {
    const accountId = '1';
    it('should return profiles and sets for a contact and its ancestors', async () => {
      // Given
      const { contact1 } = await prismaClient.$transaction(async (tx) => {
        const contact1 = await tx.contacts.create({
          data: { name: 'L0', str_id: 'contact1', account_id: accountId },
        });
        const contact2 = await tx.contacts.create({
          data: { name: 'L1', account_id: accountId },
        });
        const contact3 = await tx.contacts.create({
          data: { name: 'L2', account_id: accountId },
        });

        await tx.contact_hierarchy.create({
          data: {
            parent_id: contact2.id,
            contact_id: contact1.id,
            account_id: accountId,
          },
        });
        await tx.contact_hierarchy.create({
          data: {
            parent_id: contact3.id,
            contact_id: contact2.id,
            account_id: accountId,
          },
        });

        const profileSet1 =
          await tx.agent_commission_schedule_profiles_sets.create({
            data: {
              name: 'Set 1',
              uid: 'uid1',
              account_id: accountId,
            },
          });

        await tx.contacts_agent_commission_schedule_profiles_sets.create({
          data: {
            contact_id: contact1.id,
            agent_commission_schedule_profile_set_id: profileSet1.id,
            start_date: new Date('2023-01-01'),
            account_id: accountId,
            uid: 'uid2',
          },
        });

        const profile1 = await tx.agent_commission_schedule_profiles.create({
          data: {
            name: 'Profile 1',
            uid: 'uid3',
            account_id: accountId,
          },
        });

        await tx.contacts_agent_commission_schedule_profiles.create({
          data: {
            contact_id: contact2.id,
            agent_commission_schedule_profile_id: profile1.id,
            start_date: new Date('2023-01-01'),

            account_id: accountId,
            uid: 'uid4',
          },
        });

        return { contact1, contact2, contact3 };
      });

      const ancestors = await findContactWithAncestorsCTE(
        contact1.str_id as string,
        new Date('2023-05-01')
      );
      const contactIds = (ancestors as { id: number; level: number }[]).map(
        (a) => a.id
      );

      await asyncLocalStorage.run(
        { account: { account_id: accountId } as ExtAccountInfo, traceId: '1' },
        async () => {
          const result = await service.getCompProfiles({
            contactIds,
            effectiveDate: new Date('2023-05-01'),
          });

          expect(result.compProfileSets).toHaveLength(1);
          expect(result.profiles).toHaveLength(1);
        }
      );
    });
  });
});
