import { AccountIds, WorkerNames } from 'common/constants';
import { inject, injectable } from 'inversify';
import isNil from 'lodash-es/isNil';

import dayjs from '@/lib/dayjs';
import { Cache } from '@/lib/decorators';
import { getValidDate, limitConcurrency } from '@/lib/helpers';
import type { ConfigItemValueForDataSync } from '@/services/account-processor-config/interfaces';
import { AgencyIntegratorService } from '@/services/agencyIntegrator';
import { OperationTypeCodes } from '@/services/agencyIntegrator/interfaces';
import { DataProcessService } from '@/services/data_processing';
import { AppLoggerService } from '@/services/logger/appLogger';
import { MyAdvisorGridsService } from '@/services/myadvisorgrids';
import type {
  AgentCommissionLevelAppointment,
  AgentCommissionLevelData,
  AgentHierarchyData,
  Carrier,
  CarrierEffectiveDate,
  CarrierEffectiveDateList,
  CarrierList,
  CarrierProductCommissionData,
  CarrierProductData,
} from '@/services/myadvisorgrids/interface';
import type { DBData } from '@/services/queue/worker/base';
import {
  type AgentHierarchyDatum,
  type AgentLevelDatum,
  BaseWorker,
  type CompanyDatum,
  type CompGridCriteriaDatum,
  type CompGridDatum,
  type CompGridLevelDatum,
  type CompGridProductDatum,
  type CompGridRateDatum,
  type DateRangeDatum,
  type IDataSyncWorker,
} from '@/services/queue/worker/base';
import {
  DataProcessingStatuses,
  DataProcessingTypes,
  DataStates,
  type ExtAccountInfo,
} from '@/types';
import { CompensationType } from '@/types/enum/compensation-type.enum';
import type { Entity, SyncParamsDTO } from 'common/dto/data_processing/sync';
import { prismaClient } from '@/lib/prisma';
import CompGridsService from '@/services/comp-grids/CompGridsService';
import { BusinessException } from '@/lib/exceptionHandler';

@injectable()
export class MyAdvisorGridsWorker
  extends BaseWorker
  implements IDataSyncWorker<unknown>
{
  name = WorkerNames.MyAdvisorGridsWorker;
  logger: AppLoggerService = new AppLoggerService({
    defaultMeta: {
      service: WorkerNames.MyAdvisorGridsWorker,
    },
  });

  @inject(CompGridsService)
  compGridsService!: CompGridsService;

  @inject(MyAdvisorGridsService)
  // @ts-expect-error
  myAdvisorGridsService: MyAdvisorGridsService;

  @inject(AgencyIntegratorService)
  // @ts-expect-error
  agencyIntegratorService: AgencyIntegratorService;

  @inject(DataProcessService)
  // @ts-expect-error
  dataProcessingService: DataProcessService;

  constructor() {
    super();
    this.registerDataSource({
      carriersAndProducts: this.fetchCompanies.bind(this),
      compGrids: this.fetchCompGrids.bind(this),
      agentHierarchy: this.fetchAgentHierarchy.bind(this),
      agentLevel: this.fetchAgentLevel.bind(this),
    });
    this.registerTransformer({
      companyTransform: {
        // @ts-expect-error
        getData: this.getCompanies.bind(this),
      },
      compGridTransform: {
        // @ts-expect-error
        getData: this.getCompGridData.bind(this),
      },
      compGridLevelTransform: {
        // @ts-expect-error
        getData: this.getCompGridLevels.bind(this),
      },
      compGridProductTransform: {
        // @ts-expect-error
        getData: this.getCompGridProducts.bind(this),
      },
      compCriteriaTransform: {
        // @ts-expect-error
        getData: this.getCompGridCriteria.bind(this),
      },
      dateRangeTransform: {
        // @ts-expect-error
        getData: this.getDateRanges.bind(this),
      },
      compGridRateTransform: {
        // @ts-expect-error
        getData: this.getCompGridRates.bind(this),
      },
      agentHierarchyTransform: {
        // @ts-expect-error
        getData: this.getAgentHierarchyData.bind(this),
      },
      agentLevelTransform: {
        // @ts-expect-error
        getData: this.getAgentLevelData.bind(this),
      },
    });
  }

  async setup(account: ExtAccountInfo) {
    const config = await this.configItemService.getWorkerConfig<
      ConfigItemValueForDataSync<{
        apiKey: string;
      }>
    >({ account_id: account.account_id, worker: this.name });
    // @ts-expect-error
    this.myAdvisorGridsService.loadConfig(config.value.credentials);
    const agentConfig = await this.configItemService.getWorkerConfig<
      ConfigItemValueForDataSync<{
        FileControlID: number;
        LoginName: string;
        Password: string;
      }>
    >({
      account_id: account.account_id,
      worker: WorkerNames.AgencyIntegratorWorker,
    });
    // @ts-expect-error
    this.agencyIntegratorService.loadConfig(agentConfig.value.credentials);
  }

  @Cache('CarrierList')
  async fetchCompanies() {
    const carriers = await this.myAdvisorGridsService.getCarrierDataList();
    return carriers;
  }

  @Cache('CompGrids')
  async fetchCompGrids(_dbData: DBData, account: ExtAccountInfo) {
    // Check for the last successful sync record
    const lastSync = await this.dataProcessingService.getLastTask({
      account_id: account.account_id,
      type: DataProcessingTypes.data_sync,
      status: DataProcessingStatuses.COMPLETED,
      worker: this.name,
      entity: 'compGrids',
    });

    const compGridIds = this.task.payload?.compGridIds as number[];
    const compGrids =
      await this.compGridsService.getCompGridsByIds(compGridIds);

    if (compGridIds.length && compGrids.length === 0) {
      throw BusinessException.from(
        `No comp grid data found for ids: ${compGridIds.join(', ')}`
      );
    }

    const syncIds = compGrids.map((r) => r.sync_id);
    const carriers = await this.myAdvisorGridsService.getCarrierDataList();
    // Filter carrier types based on updated_at_timestamp if we have a last sync
    let filteredCarriers = (this.task.payload as SyncParamsDTO).compGridIds
      ? carriers.filter((r) =>
          r.carrier_types.some((r) => syncIds.includes(`${r.carrier_id}`))
        )
      : carriers;
    if (lastSync && !this.task.payload.isFullSync && !compGridIds.length) {
      const lastSyncDate = dayjs(lastSync.created_at).subtract(1, 'day');
      this.logger.info(
        `Filtering carrier types updated since: ${lastSyncDate.format('YYYY-MM-DD')}`
      );

      filteredCarriers = carriers
        .map((carrier) => ({
          ...carrier,
          carrier_types: carrier.carrier_types.filter((carrierType) => {
            if (!carrierType.updated_at_timestamp) {
              return false;
            }
            const updatedAt = dayjs(carrierType.updated_at_timestamp);
            return updatedAt.isAfter(lastSyncDate);
          }),
        }))
        .filter((carrier) => carrier.carrier_types.length > 0);

      const totalCarrierTypes = carriers.reduce(
        (sum, c) => sum + c.carrier_types.length,
        0
      );
      const filteredCarrierTypes = filteredCarriers.reduce(
        (sum, c) => sum + c.carrier_types.length,
        0
      );

      this.logger.info(
        `Filtered ${filteredCarrierTypes} carrier types out of ${totalCarrierTypes} total carrier types across ${filteredCarriers.length} carriers`
      );
    } else {
      this.logger.info(
        compGrids.length
          ? `Syncing comp grids: ${compGrids.map((c) => c.name).join(', ')}`
          : 'No previous sync found, fetching all carriers'
      );
    }

    // Get carrier IDs from filtered carriers
    const carrierIds = filteredCarriers.flatMap((c) =>
      c.carrier_types.map((t) => t.carrier_id)
    );

    if (carrierIds.length === 0) {
      this.logger.info('No carriers to update');
      return { carriers: filteredCarriers, details: [] };
    }

    const details = await limitConcurrency<Carrier>(
      (id) => this.myAdvisorGridsService.getCarrierData(id),
      carrierIds,
      10
    );

    return { carriers: filteredCarriers, details };
  }

  @Cache('AgentHierarchy')
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  // biome-ignore lint/correctness/noUnusedFunctionParameters: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  async fetchAgentHierarchy(dbData: any, account: ExtAccountInfo) {
    // Check for the last successful sync record
    const lastSync = await this.dataProcessingService.getLastTask({
      account_id: account.account_id,
      type: DataProcessingTypes.data_sync,
      status: DataProcessingStatuses.COMPLETED,
      worker: this.name,
      entity: 'agentHierarchy',
    });

    // If no previous sync record exists, set last_updated to one month ago
    // If there is a record, set it to one day before the last sync
    let last_updated: string;
    if (!lastSync || this.task.payload.isFullSync) {
      last_updated = dayjs().subtract(1, 'month').format('YYYY-MM-DD');
    } else {
      last_updated = dayjs(lastSync.created_at)
        .subtract(1, 'day')
        .format('YYYY-MM-DD');
    }

    // Use last_updated as start_updated_date and today as end_updated_date
    const start_updated_date = last_updated;
    const end_updated_date = dayjs().format('YYYY-MM-DD');

    this.logger.info(
      `Fetching agent hierarchy with start_updated_date: ${start_updated_date}, end_updated_date: ${end_updated_date}`
    );

    const hierarchy = await this.myAdvisorGridsService.getAgentHierarchy(
      start_updated_date,
      end_updated_date
    );
    return hierarchy;
  }

  @Cache('AgentLevel')
  // biome-ignore lint/correctness/noUnusedFunctionParameters: dbData parameter kept for compatibility with base worker interface
  async fetchAgentLevel(dbData: DBData, account: ExtAccountInfo) {
    // Check for the last successful sync record for incremental updates
    const lastSync = await this.dataProcessingService.getLastTask({
      account_id: account.account_id,
      type: DataProcessingTypes.data_sync,
      status: DataProcessingStatuses.COMPLETED,
      worker: this.name,
      entity: 'agentLevel',
    });

    let startDate: string | undefined;

    // If we have a last sync, use it minus 7 days for incremental update
    if (lastSync && !this.task.payload?.isFullSync) {
      startDate = dayjs(lastSync.created_at)
        .subtract(7, 'days')
        .format('YYYY-MM-DD');

      this.logger.info('Performing incremental agent level sync', {
        lastSync: lastSync.created_at,
        startDate,
      });
    } else {
      this.logger.info('Performing full agent level sync');
    }

    try {
      // Fetch all agent commission levels in one API call
      const levels = await this.myAdvisorGridsService.getAgentCommissionLevel(
        undefined, // no contact_id to get all contacts
        startDate,
        undefined // no end_date
      );

      return levels || [];
    } catch (error) {
      this.logger.error('Failed to fetch agent commission levels', { error });
      throw error;
    }
  }

  // ===== COMPANIES =====
  getCompanies(data: CarrierList[]) {
    const result: { name: string; ai_id: string }[] = [];
    for (const carrier of data) {
      const typeWithAiId = carrier.carrier_types.find(
        (r) => r.carrier_ai_mapping_data.carrier_ai_id
      );
      // Only keep the carrier if there is a carrier type with an ai_id
      if (typeWithAiId) {
        result.push({
          name: `${carrier.carrier_name}`,
          ai_id: typeWithAiId.carrier_ai_mapping_data.carrier_ai_id,
        });
      }
    }
    return result;
  }
  companyTransform(
    data: { name: string; ai_id?: string },
    dbData?: DBData
  ): CompanyDatum {
    return {
      // @ts-expect-error
      sync_id: data?.ai_id?.toString(),
      company_name:
        // @ts-expect-error
        dbData?.companies?.get(data?.ai_id)?.company_name || data.name,
      sync_worker: data?.ai_id ? WorkerNames.AgencyIntegratorWorker : this.name,
      type: ['Carrier'],
    };
  }

  getCompGridData(
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data: { carriers: CarrierList[]; details: any[] },
    _dbData?: DBData
  ) {
    const result: { name: string; carrier_id: string; ai_id: string }[] = [];
    for (const carrier of data.carriers) {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      carrier.carrier_types.forEach((r) => {
        if (r.carrier_ai_mapping_data.carrier_ai_id) {
          result.push({
            name: `${carrier.carrier_name} - ${r.carrier_type_name}`,
            carrier_id: r.carrier_id.toString(),
            ai_id: r.carrier_ai_mapping_data.carrier_ai_id,
          });
        }
      });
    }
    return result;
  }

  compGridTransform(
    data: { name: string; carrier_id: string; ai_id: string },
    dbData?: DBData
  ): CompGridDatum {
    return {
      sync_id: data?.carrier_id?.toString(),
      name: data?.name,
      company_id: dbData?.companies?.get(data?.ai_id)?.id,
    };
  }

  generateAgencyLevelKey(carrier_id: string | number) {
    return ['Carrier', carrier_id].join('-');
  }

  getCompGridLevels(data: { details: Carrier[] }, dbData?: DBData) {
    const result: {
      name: string;
      carrier_id: string;
      ai_id: string;
      type?: 'Agency level';
      level_id: string;
    }[] = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.details.forEach((d) => {
      if (
        !d.carrier_ai_mapping_data?.carrier_ai_id ||
        !dbData?.comp_grids?.get(d?.carrier_id?.toString())?.id
      ) {
        return;
      }
      // Populate the ba_total_comp level for each carrier
      result.push({
        name: 'BA Total Comp',
        carrier_id: d?.carrier_id.toString(),
        ai_id: d?.carrier_ai_mapping_data.carrier_ai_id,
        type: 'Agency level',
        // Since this is a faked level, we use the carrier_id as the level_id
        level_id: this.generateAgencyLevelKey(d?.carrier_id),
      });

      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      d.carrier_level.forEach((l) => {
        result.push({
          name: l.carrier_level_name,
          carrier_id: d.carrier_id.toString(),
          ai_id: d.carrier_ai_mapping_data.carrier_ai_id,
          level_id: l.carrier_level_id.toString(),
        });
      });
    });
    return result;
  }

  compGridLevelTransform(
    data: {
      name: string;
      carrier_id: string;
      ai_id: string;
      level_id: string;
      type?: 'Agency level';
    },
    dbData?: DBData
  ): CompGridLevelDatum {
    return {
      sync_id: data?.level_id?.toString(),
      name: data?.name,
      comp_grid_id: dbData?.comp_grids?.get(data?.carrier_id)?.id,
      type: data?.type,
    };
  }

  extractProductCarrierMapping(details: Carrier[]) {
    const productCarrierMap = new Map<number, number>();
    const productIds: number[] = [];
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    details.forEach((d) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      d.carrier_product.forEach((p) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        p.carrier_product_data
          .filter((r) => r.carrier_product_ai_mapping_data.product_ai_id)
          .forEach((r) => {
            productCarrierMap.set(
              +r.carrier_product_ai_mapping_data.product_ai_id,
              d.carrier_id
            );
            productIds.push(+r.carrier_product_ai_mapping_data.product_ai_id);
          });
      });
    });
    return { productCarrierMap, productIds };
  }

  async getCompGridProducts(data: { details: Carrier[] }, dbData?: DBData) {
    const { productCarrierMap, productIds } = this.extractProductCarrierMapping(
      data.details
    );

    const uniqueProductIds = [...new Set(productIds)];

    const productDetails = await limitConcurrency<{
      productId: number;
      productName: string;
      carrierId: number;
      productType: string;
    }>(
      async (id) => {
        // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        let productName;
        // biome-ignore lint/suspicious/noImplicitAnyLet: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        let productType;
        if (dbData?.company_products?.get(id)) {
          return {
            productId: id,
            carrierId: productCarrierMap.get(id),
            productName: dbData?.company_products?.get(id)?.product_name,
            productType: dbData?.company_products?.get(id)?.product_type,
          };
        }
        const data = await this.agencyIntegratorService.planSearch({
          filters: [
            {
              ObjectType: { '@tc': 35, '#text': 'PolicyProduct' },
              PropertyName: 'ProductCode',
              PropertyValue: id,
              Operation: { '@tc': OperationTypeCodes.Equal },
            },
          ],
        });
        if (Array.isArray(data.PolicyProduct)) {
          const policyProducts = data.PolicyProduct.filter(
            (r) => r?.ProductCode === id
          );
          productName = policyProducts[0]?.PlanName;
          productType = policyProducts[0]?.PolicyProductTypeCode;
        } else {
          const policyProduct = data?.PolicyProduct;
          productName = policyProduct?.PlanName;
          productType = policyProduct?.PolicyProductTypeCode;
        }
        if (!productName) {
          return null;
        }
        return {
          productId: id,
          productName,
          productType,
          carrierId: productCarrierMap.get(id),
        };
      },
      uniqueProductIds,
      60,
      { retries: 0 }
    );

    return productDetails.filter((r) => r);
  }

  compGridProductTransform(
    data: {
      carrierId: number;
      productId: number;
      productName: string;
      productType: string;
    },
    dbData?: DBData
  ): CompGridProductDatum {
    const productId = dbData?.company_products?.get(
      data?.productId?.toString()
    )?.id;
    let notes = dbData?.company_products?.get(
      data?.productId?.toString()
    )?.notes;
    const migrationNote = 'Migration from MyAdvisorGrids';
    notes = notes?.includes(migrationNote)
      ? notes
      : [migrationNote, notes].filter(Boolean).join('\n');

    return {
      sync_id: data?.productId?.toString(),
      name: data?.productName,
      type: data?.productType,
      notes,
      company_products: productId
        ? {
            connect: {
              id: productId,
            },
          }
        : undefined,
      comp_grid_id: dbData?.comp_grids?.get(data?.carrierId?.toString())?.id,
    };
  }

  getCompGridCriteria(data: { details: Carrier[] }, dbData?: DBData) {
    const result: (CarrierProductData & {
      product_id: string;
      carrier_id: string;
      ai_id: string;
      compensation_type?: string;
    })[] = [];
    const details = data.details;
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    details.forEach((d) => {
      if (
        !d.carrier_ai_mapping_data?.carrier_ai_id ||
        !dbData?.companies?.get(
          d.carrier_ai_mapping_data?.carrier_ai_id?.toString()
        )?.id ||
        !dbData?.comp_grids?.get(d?.carrier_id?.toString())?.id
      ) {
        return;
      }
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      d.carrier_product.forEach((p) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        p.carrier_product_data.forEach((p) => {
          const aiProductId = p?.carrier_product_ai_mapping_data?.product_ai_id;
          if (!dbData?.comp_grid_products?.get(aiProductId?.toString())?.id) {
            return;
          }
          const compGridCriteria = this.generateCriteria(p);
          const compensationTypes = this.getCompensationTypes(
            p,
            compGridCriteria.policy_year_start
          );
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          compensationTypes.forEach((t) => {
            result.push({
              ...p,
              product_id: aiProductId,
              carrier_id: d.carrier_id.toString(),
              ai_id: d.carrier_ai_mapping_data.carrier_ai_id,
              compensation_type: t,
            });
          });
        });
      });
    });
    return result;
  }

  getCompensationTypes(
    data: Required<
      Pick<
        CarrierProductData,
        | 'carrier_product_name'
        | 'carrier_product_sub_name'
        | 'carrier_product_criteria'
      >
    >,
    policy_year_start?: number
  ) {
    // TODO(neil.lu): Do we still use these fields to parse out compensation types? If not, let's remove this logic.
    const fieldsToCheck = [
      'carrier_product_name',
      'carrier_product_sub_name',
    ] as const;
    const patterns: { type: string; regex: RegExp }[] = [
      { type: CompensationType.Override, regex: /override/i },
      { type: CompensationType.Renew, regex: /renewal/i },
      { type: CompensationType.Excess, regex: /excess/i },
    ];
    const matchedTypes = new Set<string>();

    for (const field of fieldsToCheck) {
      const value = (data[field] || '').toString();
      for (const { type, regex } of patterns) {
        if (regex.test(value)) {
          matchedTypes.add(type);
        }
      }
    }

    if (matchedTypes.size === 0) {
      if (
        data.carrier_product_criteria.some(
          (c) => c.criteria_name === 'Excess Year'
        )
      ) {
        return [CompensationType.Excess];
      }
      return policy_year_start && policy_year_start > 1
        ? [CompensationType.Renew]
        : [CompensationType.Override];
    }
    return Array.from(matchedTypes);
  }

  generateCriteria(data: CarrierProductData) {
    const result: {
      issue_age_start?: number;
      issue_age_end?: number;
      policy_year_start?: number;
      policy_year_end?: number;
      geo_states?: string[];
      notes?: string;
    } = { geo_states: [] };
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data?.carrier_product_criteria?.forEach((r) => {
      if (r.criteria_name === 'Issue Age') {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        r.carrier_product_criteria_data.forEach((c) => {
          if (c.criteria_field_name === 'From Age') {
            result.issue_age_start = +c.criteria_field_value;
          } else if (c.criteria_field_name === 'To Age') {
            result.issue_age_end = +c.criteria_field_value;
          }
        });
      } else if (
        ['Excess Year', 'Policy Year', 'Renewal Year'].includes(r.criteria_name)
      ) {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        r.carrier_product_criteria_data.forEach((c) => {
          if (c.criteria_field_name === 'Start Year') {
            if (
              !isNil(result.policy_year_start) &&
              result.policy_year_start !== +c.criteria_field_value
            ) {
              const msg = `Different start years (${result.policy_year_start} !== ${+c.criteria_field_value}) found for product ${data.carrier_product_name} (${data.carrier_product_data_id}). The last criteria processed will take effect.`;
              this.logger.warn(msg);
              result.notes = this.mergeNotes(result.notes, msg);
            }
            result.policy_year_start = +c.criteria_field_value;
          } else if (c.criteria_field_name === 'End Year') {
            if (
              !isNil(result.policy_year_end) &&
              result.policy_year_end !==
                (c.criteria_field_option_value === 'Not Applicable'
                  ? result.policy_year_start
                  : +c.criteria_field_value)
            ) {
              const msg = `Different end years (${result.policy_year_end} !== ${+c.criteria_field_value}) found for product ${data.carrier_product_name} (${data.carrier_product_data_id}). The last criteria processed will take effect.`;
              this.logger.warn(msg);
              result.notes = this.mergeNotes(result.notes, msg);
            }
            result.policy_year_end = +c.criteria_field_value;
            if (c.criteria_field_option_value === 'Not Applicable') {
              result.policy_year_end = result.policy_year_start;
            }
          }
        });
      } else if (r.criteria_name === 'State') {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        r.carrier_product_criteria_data.forEach((c) => {
          if (c.criteria_field_name === 'Select States') {
            // @ts-expect-error
            result.geo_states.push(c.criteria_field_option_value);
          }
        });
      } else if (r.criteria_name === 'Sub Type') {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        r.carrier_product_criteria_data.forEach((c) => {
          if (c.criteria_field_name === 'Product Sub Type') {
            const productSubType = c.criteria_field_value;
            if (productSubType) {
              const subTypeNote = `Product Sub Type: ${productSubType}`;
              result.notes = this.mergeNotes(result.notes, subTypeNote);
            }
          }
        });
      }
    });
    return result;
  }

  mergeNotes(existingNotes?: string, newNotes?: string): string | undefined {
    if (existingNotes && newNotes && !existingNotes.includes(newNotes)) {
      return `${existingNotes}\n${newNotes}`;
    } else if (newNotes) {
      return newNotes;
    } else if (existingNotes) {
      return existingNotes;
    }
    return undefined;
  }

  compCriteriaTransform(
    data: CarrierProductData & {
      carrier_product_data_id: string;
      carrier_id: string;
      ai_id: string;
      product_id: string;
      compensation_type?: string;
    },
    dbData?: DBData
  ): CompGridCriteriaDatum {
    const details = this.generateCriteria(data);

    // Handle notes concatenation
    const existingCriteria = dbData?.comp_grid_criteria?.get(
      data?.carrier_product_data_id
    );
    const existingNotes = existingCriteria?.notes;
    const newNotes = details.notes;

    // @ts-expect-error
    const finalNotes = this.mergeNotes(existingNotes, newNotes);

    return {
      sync_id: data?.carrier_product_data_id,
      grid_product_id: undefined,
      comp_grid_product: {
        connect: {
          // @ts-expect-error
          id: dbData?.comp_grid_products?.get(data?.product_id?.toString())?.id,
        },
      },
      comp_grid_id: undefined,
      comp_grid: {
        // @ts-expect-error
        connect: { id: dbData?.comp_grids?.get(data?.carrier_id)?.id },
      },
      company: {
        // @ts-expect-error
        connect: { id: dbData?.companies?.get(data?.ai_id)?.id },
      },
      company_id: undefined,
      ...details,
      notes: finalNotes,
      compensation_type: data?.compensation_type,
    };
  }

  generateDateRanges = (
    data: CarrierEffectiveDateList
  ): (CarrierEffectiveDate & { start_date: Date; end_date: Date })[] => {
    const { carrier_effective_dates } = data;

    // Return empty array if no dates are provided
    if (!carrier_effective_dates || carrier_effective_dates.length === 0) {
      return [];
    }

    // Sort dates chronologically. Create a copy to avoid modifying the original array.
    const sortedDates = [...carrier_effective_dates].sort((a, b) =>
      a.carrier_effective_date.localeCompare(b.carrier_effective_date)
    );

    // Handle the case with only one effective date
    if (sortedDates.length === 1) {
      // @ts-expect-error
      return sortedDates.map((d) => ({
        ...d,
        start_date: getValidDate(d.carrier_effective_date),
        end_date: null,
      }));
    }

    // Generate ranges for multiple dates
    const ranges: (CarrierEffectiveDate & {
      start_date: Date;
      end_date: Date;
    })[] = [];
    for (let i = 0; i < sortedDates.length; i++) {
      const startDate = sortedDates[i].carrier_effective_date;
      let endDate: string | null = null;

      // If it's not the last date, the end date is the start date of the next period
      if (i < sortedDates.length - 1) {
        endDate = sortedDates[i + 1].carrier_effective_date;
      }
      // For the last date, the end date is null (extends indefinitely)

      ranges.push({
        ...sortedDates[i],
        // @ts-expect-error
        start_date: getValidDate(startDate),
        // @ts-expect-error
        end_date: endDate ? getValidDate(endDate) : null,
      });
    }

    return ranges;
  };

  async getDateRanges(data: { details: Carrier[] }) {
    const batchSize = 10;
    // Get all effective dates via call getCarrierEffectiveDateList, use limitConcurrency
    const effectiveDates = await limitConcurrency<CarrierEffectiveDateList[]>(
      (id) => this.myAdvisorGridsService.getCarrierEffectiveDateList(id),
      data.details.map((d) => d.carrier_id),
      batchSize
    );
    const taskData = effectiveDates
      .map((r) => {
        return r.map((item) => {
          item.carrier_effective_dates = this.generateDateRanges(item);
          return item;
        });
      })
      .flatMap((r) =>
        r.flatMap((i) =>
          i.carrier_effective_dates.map((d) => ({
            carrier_id: i.carrier_id,
            ...d,
          }))
        )
      );

    return taskData as unknown as (CarrierEffectiveDate & {
      carrier_id: string;
      start_date: Date;
      end_date: Date;
    })[];
  }

  dateRangeTransform(
    data: Awaited<ReturnType<typeof this.getDateRanges>>[0]
  ): DateRangeDatum {
    return {
      sync_id: data?.carrier_effective_date_id?.toString(),
      start_date: data?.start_date,
      end_date: data?.end_date,
    };
  }

  generateBaRateKey(carrierId: string, productDataId: string) {
    return ['Carrier', carrierId, 'Product', productDataId].join('-');
  }
  async getCompGridRates(data: { details: Carrier[] }, dbData?: DBData) {
    const result: (Partial<CarrierProductCommissionData> & {
      carrier_product_data_id: string;
      carrier_product_ba_total_comp: string;
      carrier_id: string;
      carrier_effective_date_id: number;
      ai_id: string;
      range_ids: string[];
    })[] = [];
    const details = data.details;
    const ranges = await this.getDateRanges({ details });
    const allVersions = await limitConcurrency<Carrier>(
      (data: CarrierEffectiveDate & { carrier_id: string }) =>
        this.myAdvisorGridsService.getCarrierData(
          +data.carrier_id,
          data.carrier_effective_date_id
        ),
      ranges,
      10
    );
    // Create a map of <carrier_product_commission_id, carrier_effective_date_id[]>
    const carrierProductCommissionMap = new Map<number, string[]>();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    allVersions.forEach((v) => {
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      v.carrier_product.forEach((p) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        p.carrier_product_data.forEach((c) => {
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          c.carrier_product_commission_data.forEach((r) => {
            carrierProductCommissionMap.set(r.carrier_product_commission_id, [
              ...(carrierProductCommissionMap.get(
                r.carrier_product_commission_id
              ) || []),
              v.carrier_effective_date_data?.carrier_effective_date_id?.toString(),
            ]);
          });
        });
      });
    });
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    details.forEach((d) => {
      if (!d.carrier_ai_mapping_data?.carrier_ai_id) {
        return;
      }
      // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      d.carrier_product.forEach((p) => {
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        p.carrier_product_data.forEach((c) => {
          if (dbData?.comp_grid_criteria?.get(c?.carrier_product_data_id)?.id) {
            result.push({
              carrier_product_commission_id: this.generateBaRateKey(
                d.carrier_id.toString(),
                c.carrier_product_data_id
                // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
              ) as any,
              carrier_level_id: this.generateAgencyLevelKey(d.carrier_id),
              carrier_product_commission_value: c.carrier_product_ba_total_comp,
              carrier_product_ba_total_comp: c.carrier_product_ba_total_comp,
              carrier_product_data_id: c.carrier_product_data_id,
              carrier_id: d.carrier_id.toString(),
              ai_id: d.carrier_ai_mapping_data.carrier_ai_id,
              carrier_effective_date_id:
                +d.carrier_effective_date_data.carrier_effective_date_id,
              range_ids: [],
            });
          }
          // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          c.carrier_product_commission_data.forEach((r) => {
            if (
              !dbData?.comp_grid_criteria?.get(c?.carrier_product_data_id)
                ?.id ||
              !dbData?.comp_grid_levels?.get(r?.carrier_level_id)?.id
            ) {
              return;
            }
            result.push({
              ...r,
              carrier_product_ba_total_comp: c.carrier_product_ba_total_comp,
              carrier_product_data_id: c.carrier_product_data_id,
              carrier_id: d.carrier_id.toString(),
              ai_id: d.carrier_ai_mapping_data.carrier_ai_id,
              carrier_effective_date_id:
                +d.carrier_effective_date_data.carrier_effective_date_id,
              range_ids:
                carrierProductCommissionMap.get(
                  r.carrier_product_commission_id
                ) || [],
            });
          });
        });
      });
    });
    // @ts-expect-error
    return result.filter((r) => +r.carrier_product_commission_value);
  }

  compGridRateTransform(
    data: Awaited<ReturnType<typeof this.getCompGridRates>>[0],
    dbData?: DBData
  ): CompGridRateDatum {
    return {
      // @ts-expect-error
      sync_id: data?.carrier_product_commission_id?.toString(),
      comp_grid_criterion: {
        connect: {
          // @ts-expect-error
          id: dbData?.comp_grid_criteria?.get(data?.carrier_product_data_id)
            ?.id,
        },
      },
      comp_grid_criterion_id: undefined,
      comp_grid_level: {
        connect: {
          // @ts-expect-error
          id: dbData?.comp_grid_levels?.get(data?.carrier_level_id)?.id,
        },
      },
      comp_grid_level_id: undefined,
      date_ranges: {
        // Connect to all date ranges that have the same carrier_effective_date_id
        // @ts-expect-error
        connect: data?.range_ids
          ?.map((id) => ({
            id: dbData?.date_ranges?.get(id)?.id,
          }))
          .filter((r) => r.id),
      },
      // @ts-expect-error
      rate: +data?.carrier_product_commission_value,
      // @ts-expect-error
      carrier_rate: +data?.carrier_product_commission_value,
    };
  }

  getAgentHierarchyData(data: AgentHierarchyData[], dbData: DBData) {
    const existingHierarchies = new Set<string>();
    if (dbData.contact_hierarchy) {
      for (const hierarchy of dbData.contact_hierarchy.values()) {
        if (hierarchy.contact_id && hierarchy.parent_id) {
          existingHierarchies.add(
            `${hierarchy.contact_id}-${hierarchy.parent_id}`
          );
        }
      }
    }

    // First filter records that have corresponding contacts in the database
    const validRecords = data.filter((record) => {
      if (!record?.agent_contact_id || !record?.hierarchy_parent_contact_id) {
        return false;
      }
      const contact = dbData?.contacts?.get(`${record?.agent_contact_id}`);
      const parentContact = dbData?.contacts?.get(
        `${record?.hierarchy_parent_contact_id}`
      );

      if (!contact || !parentContact) {
        return false;
      }

      // If a record with the same contact_id and parent_id already exists, skip it
      if (existingHierarchies.has(`${contact.id}-${parentContact.id}`)) {
        return false;
      }

      return true;
    });

    // Group records by agent_contact_id to check for consistency
    const agentGroups = new Map<string, AgentHierarchyData[]>();
    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    validRecords.forEach((record) => {
      const agentId = record.agent_contact_id?.toString();
      if (agentId) {
        if (!agentGroups.has(agentId)) {
          agentGroups.set(agentId, []);
        }
        // biome-ignore lint/style/noNonNullAssertion: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        agentGroups.get(agentId)!.push(record);
      }
    });

    const result: AgentHierarchyData[] = [];

    // Process each agent group
    agentGroups.forEach((records, agentId) => {
      // Get all unique hierarchy_parent_contact_id values for this agent
      const uniqueParentIds = new Set(
        records
          .map((r) => r.hierarchy_parent_contact_id?.toString())
          .filter(Boolean)
      );

      // Only process if all records for this agent have the same hierarchy_parent_contact_id
      if (uniqueParentIds.size === 1) {
        // Sort by last_updated_on (or submit_date as fallback) in descending order to get the latest record
        const sortedRecords = records.sort((a, b) => {
          const dateA = new Date(
            a.last_updated_on || a.submit_date || '1970-01-01'
          );
          const dateB = new Date(
            b.last_updated_on || b.submit_date || '1970-01-01'
          );
          return dateB.getTime() - dateA.getTime();
        });

        // Take only the latest record
        result.push(sortedRecords[0]);
      } else {
        // Log inconsistent data for debugging
        this.logger.warn(
          `Agent ${agentId} has inconsistent hierarchy_parent_contact_id values: ${Array.from(uniqueParentIds).join(', ')}. Skipping this agent.`
        );
      }
    });

    return result;
  }

  agentHierarchyTransform(
    data: AgentHierarchyData,
    dbData?: DBData
  ): AgentHierarchyDatum {
    const sync_id = data?.application_id;

    const contact = dbData?.contacts?.get(data?.agent_contact_id?.toString());
    const parentContact = dbData?.contacts?.get(
      // @ts-expect-error
      data?.hierarchy_parent_contact_id?.toString()
    );

    return {
      sync_id,
      // @ts-expect-error
      contact_id: contact?.id,
      parent_id: parentContact?.id,
    };
  }

  getAgentLevelData(data: AgentCommissionLevelData[], dbData: DBData) {
    const result: (AgentCommissionLevelData & {
      appointment: AgentCommissionLevelAppointment;
    })[] = [];

    // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    data.forEach((record) => {
      // Only process records that have a corresponding contact in the database
      if (dbData?.contacts?.get(record?.contact_id)) {
        const appointments = record?.appointments;
        // @ts-expect-error
        delete record.appointments;
        // Process all appointments for this contact
        // biome-ignore lint/complexity/noForEach: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
        appointments?.forEach((appointment) => {
          // Only include appointments with "Approved" status
          if (appointment.status === 'Approved') {
            result.push({
              ...record,
              appointment: appointment,
            });
          }
        });
      }
    });

    return result;
  }

  agentLevelTransform(
    data: AgentCommissionLevelData & {
      appointment: AgentCommissionLevelAppointment;
    },
    dbData?: DBData
  ): AgentLevelDatum {
    const appointment = data?.appointment;
    return {
      sync_id: appointment?.id?.toString(),
      loa: appointment?.loa_badge,
      company_id:
        dbData?.companies?.get(appointment?.ai?.carrier_id?.toString())?.id ||
        dbData?.companies?.get(appointment?.mag?.carrier_id?.toString())?.id,
      // @ts-expect-error
      contact_id: dbData?.contacts?.get(data?.contact_id?.toString())?.id,
      level_label: appointment?.mag?.carrier_level?.carrier_level_name,
    };
  }

  async afterSyncHook(context: {
    entity: Entity;
    data: unknown;
    _taskData?: unknown[];
    dbData: DBData;
  }) {
    const { entity, data } = context;

    if (
      entity === 'agentHierarchy' &&
      this.task?.account?.account_id === AccountIds.BROKERS_ALLIANCE
    ) {
      await this.populateTeamCode(data as AgentHierarchyData[], context.dbData);
    }
  }

  async populateTeamCode(data: AgentHierarchyData[], dbData: DBData) {
    const policies = await prismaClient.report_data.findMany({
      where: {
        effective_date: {
          gte: new Date('2024-01-01'),
        },
        state: DataStates.ACTIVE,
      },
      select: {
        policy_id: true,
      },
    });
    const existPolicyMap = new Map(
      policies.map((policy) => [policy.policy_id, true])
    );

    const taskData = data.filter((r) => existPolicyMap.get(r.policy_number));

    return await limitConcurrency(
      async (item: AgentHierarchyData) => {
        if (!item.policy_number) {
          return;
        }
        if (!dbData.report_data.get(item.policy_number)) {
          this.logger.warn(
            `Policy data does not exist for policy number ${item.policy_number}. Skipping.`
          );
          return;
        }
        this.logger.info(
          `Updating group_name ${item.code} and group_id ${item.team_code} for policy number ${item.policy_number}`
        );
        const reportDataId = dbData.report_data.get(item.policy_number)?.id;
        if (!reportDataId) {
          return;
        }
        return await prismaClient.report_data.update({
          where: {
            id: reportDataId,
          },
          data: {
            group_name: item.code,
            group_id: item.team_code,
          },
        });
      },
      taskData,
      30
    );
  }
}
