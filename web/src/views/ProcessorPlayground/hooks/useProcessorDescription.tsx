import { useState, useCallback, useEffect, useMemo } from 'react';
import { useDropzone } from 'react-dropzone';

import API from '@/services/API';
import useSnackbar from '@/contexts/useSnackbar';
import useDownloadStorageFile from '@/contexts/useDownloadStorageFile';
import { useImageManager, type UploadedImage } from './useImageManager';

const SUPPORTED_FORMATS = ['.png', '.jpg', '.jpeg', '.gif', '.webp'];

interface ParsedDescription {
  text: string;
  images: Array<{
    id: string;
    file_path?: string;
    name: string;
  }>;
}

interface UseProcessorDescriptionProps {
  processorId?: number;
  processorStrId?: string;
  initialDescription?: string;
  onSave?: (description: string) => void;
}

export const useProcessorDescription = ({
  processorId,
  processorStrId,
  initialDescription = '',
  onSave,
}: UseProcessorDescriptionProps) => {
  const [description, setDescription] = useState('');
  const [images, setImages] = useState<UploadedImage[]>([]);
  const [uploading, setUploading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  const { showSnackbar } = useSnackbar();
  const { downloadFile } = useDownloadStorageFile();
  const { processImage, createBlobUrl, cleanupBlobUrls, removeImage } =
    useImageManager(downloadFile);

  const uploadImageMutation = API.getMutation(
    'admin/processors/descriptions',
    'POST'
  );
  const updateProcessorMutation = API.getMutation('admin/processors', 'PATCH');

  const parsedDescription = useMemo((): ParsedDescription | null => {
    if (!initialDescription) return null;

    try {
      return JSON.parse(initialDescription);
    } catch {
      return null;
    }
  }, [initialDescription]);

  useEffect(() => {
    if (parsedDescription) {
      setDescription(parsedDescription.text || '');
      setIsEditing(false);

      if (parsedDescription.images?.length > 0) {
        const loadingImages = parsedDescription.images.map((img) => ({
          ...img,
          url: '',
          isLoading: true,
          hasError: false,
        }));
        setImages(loadingImages);

        Promise.all(parsedDescription.images.map(processImage)).then(setImages);
      } else {
        setImages([]);
      }
    } else if (initialDescription && typeof initialDescription === 'string') {
      setDescription(initialDescription);
      setImages([]);
      setIsEditing(false);
    } else {
      setDescription('');
      setImages([]);
      setIsEditing(true);
    }
  }, [parsedDescription, initialDescription, processImage]);

  useEffect(() => {
    return () => {
      cleanupBlobUrls();
    };
  }, [cleanupBlobUrls]);

  const onDrop = useCallback(
    async (acceptedFiles: File[]) => {
      if (!processorStrId) {
        showSnackbar('Processor ID is required for image upload', 'error');
        return;
      }

      setUploading(true);
      try {
        const uploadPromises = acceptedFiles.map(async (file) => {
          const fileContent = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () =>
              resolve((reader.result as string).split(',')[1]);
            reader.onerror = reject;
            reader.readAsDataURL(file);
          });

          const payload = {
            processor_id: processorStrId,
            file_content: fileContent,
            file_name: file.name,
          };

          const result = await uploadImageMutation.mutateAsync(payload);

          try {
            const downloadedFile = await downloadFile({
              endpoint_str_id: result.file_path,
              file_preview_type: 'original',
              endpoint: 'admin/processors/descriptions',
            });

            if (downloadedFile) {
              const blobUrl = createBlobUrl(downloadedFile);
              return {
                id: result.id,
                url: blobUrl,
                name: downloadedFile.name,
                file_path: result.file_path,
                isLoading: false,
                hasError: false,
              };
            }
          } catch (error) {
            console.error(`Failed to download uploaded image: ${error}`);
          }

          const blobUrl = createBlobUrl(file);
          return {
            id: result.id,
            url: blobUrl,
            name: file.name,
            file_path: result.file_path,
            isLoading: false,
            hasError: false,
          };
        });

        const uploadedImages = await Promise.all(uploadPromises);
        setImages((prev) => [...prev, ...uploadedImages]);
        showSnackbar('Images uploaded successfully', 'success');
      } catch (error) {
        console.error('Failed to upload images:', error);
        showSnackbar('Failed to upload images. Please try again.', 'error');
      } finally {
        setUploading(false);
      }
    },
    [
      processorStrId,
      uploadImageMutation,
      showSnackbar,
      downloadFile,
      createBlobUrl,
    ]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': SUPPORTED_FORMATS,
    },
    multiple: true,
  });

  const handleEdit = useCallback(() => {
    if (!processorStrId) {
      showSnackbar('Please save the processor first', 'error');
      return;
    }
    setIsEditing(true);
  }, [processorStrId, showSnackbar]);

  const handleSave = useCallback(async () => {
    if (!processorId) {
      showSnackbar('Please save the processor first', 'error');
      return;
    }

    setSaving(true);
    try {
      const descriptionData = JSON.stringify({
        text: description,
        images: images
          .filter((img) => !img.hasError)
          .map((img) => ({
            id: img.id,
            file_path: img.file_path,
            name: img.name,
          })),
      });

      await updateProcessorMutation.mutateAsync({
        id: processorId,
        description: descriptionData,
      });

      if (onSave) {
        onSave(descriptionData);
      }

      showSnackbar('Description saved successfully', 'success');
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to save description:', error);
      showSnackbar('Failed to save description. Please try again.', 'error');
    } finally {
      setSaving(false);
    }
  }, [
    processorId,
    description,
    images,
    updateProcessorMutation,
    onSave,
    showSnackbar,
  ]);

  const handleRemoveImage = useCallback(
    (imageId: string) => {
      setImages((prev) => removeImage(prev, imageId));
    },
    [removeImage]
  );

  const openPreview = useCallback((imageUrl: string) => {
    setPreviewImage(imageUrl);
  }, []);

  const closePreview = useCallback(() => {
    setPreviewImage(null);
  }, []);

  const validImages = useMemo(
    () => images.filter((img) => !img.hasError),
    [images]
  );

  return {
    description,
    setDescription,
    images,
    uploading,
    saving,
    isEditing,
    previewImage,
    validImages,
    getRootProps,
    getInputProps,
    isDragActive,
    handleEdit,
    handleSave,
    handleRemoveImage,
    openPreview,
    closePreview,
  };
};
