import { useCallback, useRef, useEffect } from 'react';

export interface UploadedImage {
  id: string;
  url: string;
  name: string;
  file_path?: string;
  isLoading?: boolean;
  hasError?: boolean;
}

export const useImageManager = (downloadFile) => {
  const blobUrlsRef = useRef<Set<string>>(new Set());

  const createBlobUrl = useCallback((file: File): string => {
    const url = URL.createObjectURL(file);
    blobUrlsRef.current.add(url);
    return url;
  }, []);

  const cleanupBlobUrls = useCallback(() => {
    for (const url of blobUrlsRef.current) {
      URL.revokeObjectURL(url);
    }
    blobUrlsRef.current.clear();
  }, []);

  const processImage = useCallback(
    async (img): Promise<UploadedImage> => {
      try {
        const file = await downloadFile({
          endpoint_str_id: img.file_path || img.id,
          file_preview_type: 'original',
          endpoint: 'admin/processors/descriptions',
        });

        if (file) {
          const blobUrl = createBlobUrl(file);
          return {
            ...img,
            url: blobUrl,
            isLoading: false,
            hasError: false,
          };
        }

        return {
          ...img,
          url: '',
          isLoading: false,
          hasError: true,
        };
      } catch (error) {
        console.error(`Failed to process image ${img.id}:`, error);
        return {
          ...img,
          url: '',
          isLoading: false,
          hasError: true,
        };
      }
    },
    [downloadFile, createBlobUrl]
  );

  const removeImage = useCallback(
    (images: UploadedImage[], imageId: string) => {
      const imageToRemove = images.find((img) => img.id === imageId);
      if (imageToRemove?.url) {
        URL.revokeObjectURL(imageToRemove.url);
      }
      return images.filter((img) => img.id !== imageId);
    },
    []
  );

  useEffect(() => {
    return cleanupBlobUrls;
  }, [cleanupBlobUrls]);

  return {
    processImage,
    createBlobUrl,
    cleanupBlobUrls,
    removeImage,
  };
};
