import { renderHook, act } from '@testing-library/react';
import { type Mock, vi } from 'vitest';

import { useImageManager, type UploadedImage } from './useImageManager';

describe('useImageManager', () => {
  let downloadFileMock: Mock;
  const mockFile = new File(['dummy content'], 'test.png', {
    type: 'image/png',
  });

  beforeEach(() => {
    downloadFileMock = vi.fn();
    vi.clearAllMocks();

    global.URL.createObjectURL = vi.fn(() => 'blob://test-url');
    global.URL.revokeObjectURL = vi.fn();
  });

  it('createBlobUrl stores URL for cleanup', () => {
    const { result } = renderHook(() => useImageManager(downloadFileMock));

    act(() => {
      const url = result.current.createBlobUrl(mockFile);
      expect(url).toBe('blob://test-url');
      expect(global.URL.createObjectURL).toHaveBeenCalledWith(mockFile);
    });

    act(() => {
      result.current.cleanupBlobUrls();
    });

    expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob://test-url');
  });

  it('processImage returns UploadedImage with blob URL', async () => {
    downloadFileMock.mockResolvedValue(mockFile);
    const img: UploadedImage = { id: '1', url: '', name: 'test' };

    const { result } = renderHook(() => useImageManager(downloadFileMock));

    const processed = await act(async () => result.current.processImage(img));

    expect(processed.url).toBe('blob://test-url');
    expect(processed.isLoading).toBe(false);
    expect(processed.hasError).toBe(false);
  });

  it('processImage returns error if downloadFile fails', async () => {
    downloadFileMock.mockRejectedValue(new Error('fail'));
    const img: UploadedImage = { id: '2', url: '', name: 'fail-test' };

    const { result } = renderHook(() => useImageManager(downloadFileMock));

    const processed = await act(async () => result.current.processImage(img));

    expect(processed.url).toBe('');
    expect(processed.isLoading).toBe(false);
    expect(processed.hasError).toBe(true);
  });

  it('removeImage revokes URL and removes image from array', () => {
    const { result } = renderHook(() => useImageManager(downloadFileMock));
    const images: UploadedImage[] = [
      { id: '1', url: 'blob://test-url', name: 'test1' },
      { id: '2', url: 'blob://test2', name: 'test2' },
    ];

    act(() => {
      const updated = result.current.removeImage(images, '1');
      expect(updated).toHaveLength(1);
      expect(updated[0].id).toBe('2');
    });

    expect(global.URL.revokeObjectURL).toHaveBeenCalledWith('blob://test-url');
  });
});
