import type { SxProps, Theme } from '@mui/material';

export const IMAGE_GRID_COLS = 3;
export const IMAGE_GRID_GAP = 12;
export const IMAGE_HEIGHT = 140;
export const DISPLAY_IMAGE_HEIGHT = 100;

export const uploadBoxStyles: SxProps<Theme> = {
  position: 'relative',
  border: '2px dashed',
  borderColor: 'primary.main',
  borderRadius: 2,
  p: 3,
  textAlign: 'center',
  cursor: 'pointer',
  mb: 2,
  transition: 'all 0.2s ease',
  '&:hover': {
    borderColor: 'primary.dark',
    bgcolor: 'primary.50',
  },
};

export const uploadingIndicatorStyles: SxProps<Theme> = {
  position: 'absolute',
  bottom: 12,
  left: 12,
  display: 'flex',
  alignItems: 'center',
  px: 2,
  py: 0.5,
  borderRadius: 20,
  bgcolor: 'rgba(255, 255, 255, 0.9)',
  backdropFilter: 'blur(4px)',
  border: '1px solid rgba(0, 0, 0, 0.1)',
  color: 'primary.main',
};

export const imageContainerStyles: SxProps<Theme> = {
  width: '100%',
  height: IMAGE_HEIGHT,
  bgcolor: 'grey.100',
  border: '1px solid',
  borderColor: 'grey.300',
  borderRadius: 2,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center',
  p: 1,
};

export const dialogTitleStyles: SxProps<Theme> = {
  pb: 2,
};

export const closeButtonStyles: SxProps<Theme> = {
  position: 'absolute',
  right: 8,
  top: 8,
  color: 'grey.500',
};

export const textFieldStyles: SxProps<Theme> = {
  mb: 3,
};

export const sectionTitleStyles: SxProps<Theme> = {
  mb: 2,
  fontWeight: 500,
};

export const descriptionTextStyles: SxProps<Theme> = {
  mb: 3,
  whiteSpace: 'pre-wrap',
  lineHeight: 1.6,
  color: 'text.primary',
};

export const noDescriptionStyles: SxProps<Theme> = {
  mb: 3,
  fontStyle: 'italic',
};

export const imageCountStyles: SxProps<Theme> = {
  ml: 1,
  fontSize: '0.875rem',
};

export const imagePreviewStyles: SxProps<Theme> = {
  position: 'relative',
  width: '100%',
  height: `${DISPLAY_IMAGE_HEIGHT}px`,
};

export const imageStyles = {
  width: '100%',
  height: '100%',
  objectFit: 'contain' as const,
  borderRadius: '4px',
  cursor: 'pointer',
};

export const deleteButtonStyles: SxProps<Theme> = {
  position: 'absolute',
  top: 4,
  right: 4,
  bgcolor: 'rgba(255, 255, 255, 0.9)',
  '&:hover': { bgcolor: 'white' },
};

export const zoomButtonStyles: SxProps<Theme> = {
  position: 'absolute',
  top: 4,
  right: 4,
  bgcolor: 'rgba(255, 255, 255, 0.9)',
  '&:hover': { bgcolor: 'white' },
};

export const imageNameStyles: SxProps<Theme> = {
  mt: 1,
  fontSize: '0.7rem',
  textAlign: 'center',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  whiteSpace: 'nowrap',
  width: '100%',
};

export const dialogActionsStyles: SxProps<Theme> = {
  px: 3,
  pb: 3,
};

export const actionButtonStyles: SxProps<Theme> = {
  mr: 1,
};

export const saveButtonStyles: SxProps<Theme> = {
  fontWeight: 500,
};

export const previewDialogStyles: SxProps<Theme> = {
  '& .MuiDialog-paper': {
    bgcolor: 'transparent',
    boxShadow: 'none',
    overflow: 'hidden',
  },
};

export const backdropStyles: SxProps<Theme> = {
  bgcolor: 'rgba(0, 0, 0, 0.9)',
  zIndex: (theme) => theme.zIndex.modal - 1,
};

export const previewContainerStyles: SxProps<Theme> = {
  position: 'relative',
  maxWidth: '90vw',
  maxHeight: '90vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
};

export const previewImageStyles = {
  maxWidth: '100%',
  maxHeight: '100%',
  objectFit: 'contain' as const,
};

export const previewCloseButtonStyles: SxProps<Theme> = {
  position: 'absolute',
  top: 16,
  right: 16,
  bgcolor: 'rgba(255, 255, 255, 0.1)',
  color: 'white',
  '&:hover': {
    bgcolor: 'rgba(255, 255, 255, 0.2)',
  },
};

export const placeholderContainerStyles: SxProps<Theme> = {
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  height: DISPLAY_IMAGE_HEIGHT,
  justifyContent: 'center',
};

export const placeholderIconStyles: SxProps<Theme> = {
  fontSize: 40,
  mb: 1,
};

export const skeletonContainerStyles: SxProps<Theme> = {
  width: '100%',
  height: DISPLAY_IMAGE_HEIGHT,
};

export const descriptionButtonStyles: SxProps<Theme> = {
  ml: 0.5,
  mr: 0.5,
  '&:hover': {
    bgcolor: 'grey.100',
  },
};

export const getDescriptionButtonColor = (
  hasDescription: boolean
): SxProps<Theme> => ({
  color: hasDescription ? 'primary.main' : 'text.secondary',
  '&:hover': {
    bgcolor: hasDescription ? 'primary.50' : 'grey.100',
  },
});
