import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  IconButton,
  Box,
  Typography,
  TextField,
  CircularProgress,
  ImageList,
  ImageListItem,
  Backdrop,
  Tooltip,
  Skeleton,
} from '@mui/material';
import {
  Close,
  CloudUpload,
  Description,
  Delete,
  ZoomIn,
  ErrorOutline,
} from '@mui/icons-material';

import { useProcessorDescription } from './hooks/useProcessorDescription';
import {
  uploadBoxStyles,
  uploadingIndicatorStyles,
  imageContainerStyles,
  dialogTitleStyles,
  closeButtonStyles,
  textFieldStyles,
  sectionTitleStyles,
  descriptionTextStyles,
  noDescriptionStyles,
  imageCountStyles,
  imagePreviewStyles,
  imageStyles,
  deleteButtonStyles,
  zoomButtonStyles,
  imageNameStyles,
  dialogActionsStyles,
  actionButtonStyles,
  saveButtonStyles,
  previewDialogStyles,
  backdropStyles,
  previewContainerStyles,
  previewImageStyles,
  previewCloseButtonStyles,
  placeholderContainerStyles,
  placeholderIconStyles,
  skeletonContainerStyles,
  descriptionButtonStyles,
  getDescriptionButtonColor,
  IMAGE_GRID_COLS,
  IMAGE_GRID_GAP,
} from './styles';

interface ProcessorDescriptionModalProps {
  open: boolean;
  onClose: () => void;
  processorId?: number;
  processorStrId?: string;
  initialDescription?: string;
  onSave?: (description: string) => void;
}

const ImagePlaceholder = ({ hasError }: { hasError?: boolean }) => (
  <Box
    sx={{
      ...placeholderContainerStyles,
      color: hasError ? 'error.main' : 'text.secondary',
    }}
  >
    {hasError ? (
      <>
        <ErrorOutline sx={placeholderIconStyles} />
        <Typography variant="caption">Failed to load</Typography>
      </>
    ) : (
      <>
        <Description sx={placeholderIconStyles} />
        <Typography variant="caption">Image not available</Typography>
      </>
    )}
  </Box>
);

const ImageSkeleton = () => (
  <Box sx={skeletonContainerStyles}>
    <Skeleton variant="rectangular" width="100%" height="100%" />
  </Box>
);

const ProcessorDescriptionModal = ({
  open,
  onClose,
  processorId,
  processorStrId,
  initialDescription = '',
  onSave,
}: ProcessorDescriptionModalProps) => {
  const {
    description,
    setDescription,
    images,
    uploading,
    saving,
    isEditing,
    previewImage,
    validImages,
    getRootProps,
    getInputProps,
    isDragActive,
    handleEdit,
    handleSave,
    handleRemoveImage,
    openPreview,
    closePreview,
  } = useProcessorDescription({
    processorId,
    processorStrId,
    initialDescription,
    onSave,
  });

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle sx={dialogTitleStyles}>
        <Typography variant="h5" component="div" fontWeight={600}>
          Processor description
        </Typography>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={closeButtonStyles}
          disabled={uploading || saving}
        >
          <Close />
        </IconButton>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mt: 1 }}>
          {isEditing ? (
            <>
              <TextField
                fullWidth
                label="Description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Enter processor description..."
                multiline
                rows={6}
                disabled={uploading || saving}
                sx={textFieldStyles}
              />

              <Typography variant="h6" sx={sectionTitleStyles}>
                Images
              </Typography>

              <Box
                {...getRootProps()}
                sx={{
                  ...uploadBoxStyles,
                  bgcolor: isDragActive ? 'primary.50' : 'grey.50',
                }}
              >
                <input {...getInputProps()} />
                {isDragActive ? (
                  <Typography
                    variant="body1"
                    color="primary.main"
                    fontWeight={500}
                  >
                    Drop the images here...
                  </Typography>
                ) : (
                  <>
                    <CloudUpload color="action" sx={{ fontSize: 48, mb: 1 }} />
                    <Typography
                      variant="body1"
                      color="text.primary"
                      sx={{ mb: 1 }}
                    >
                      Drag and drop images here, or click to select
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      Supported formats: PNG, JPG, JPEG, GIF, WebP
                    </Typography>
                  </>
                )}

                {uploading && (
                  <Box sx={uploadingIndicatorStyles}>
                    <CircularProgress
                      size={14}
                      thickness={4}
                      sx={{ mr: 1, color: 'primary.main' }}
                    />
                    <Typography
                      variant="body2"
                      sx={{ fontSize: '0.75rem', fontWeight: 500 }}
                    >
                      Uploading...
                    </Typography>
                  </Box>
                )}
              </Box>
            </>
          ) : (
            <>
              <Typography variant="h6" sx={sectionTitleStyles}>
                Description
              </Typography>
              {description ? (
                <Typography variant="body1" sx={descriptionTextStyles}>
                  {description}
                </Typography>
              ) : (
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={noDescriptionStyles}
                >
                  No description available
                </Typography>
              )}

              {validImages.length > 0 && (
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6" sx={{ fontWeight: 500 }}>
                    Images
                  </Typography>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={imageCountStyles}
                  >
                    ({validImages.length})
                  </Typography>
                </Box>
              )}
            </>
          )}

          {images.length > 0 && (
            <ImageList cols={IMAGE_GRID_COLS} gap={IMAGE_GRID_GAP}>
              {images.map((image) => (
                <ImageListItem
                  key={image.id}
                  sx={{ borderRadius: 2, overflow: 'hidden' }}
                >
                  <Box sx={imageContainerStyles}>
                    {image.isLoading ? (
                      <ImageSkeleton />
                    ) : image.url ? (
                      <Box sx={imagePreviewStyles}>
                        <img
                          src={image.url}
                          alt={image.name}
                          loading="lazy"
                          style={imageStyles}
                          onClick={() => openPreview(image.url)}
                          onKeyUp={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              openPreview(image.url);
                            }
                          }}
                        />
                        {isEditing && (
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemoveImage(image.id);
                            }}
                            sx={deleteButtonStyles}
                          >
                            <Delete fontSize="small" color="error" />
                          </IconButton>
                        )}
                        {!isEditing && (
                          <IconButton
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              openPreview(image.url);
                            }}
                            sx={zoomButtonStyles}
                          >
                            <ZoomIn fontSize="small" />
                          </IconButton>
                        )}
                      </Box>
                    ) : (
                      <ImagePlaceholder hasError={image.hasError} />
                    )}
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={imageNameStyles}
                    >
                      {image.name}
                    </Typography>
                  </Box>
                </ImageListItem>
              ))}
            </ImageList>
          )}
        </Box>
      </DialogContent>

      <DialogActions sx={dialogActionsStyles}>
        <Button
          onClick={onClose}
          disabled={uploading || saving}
          sx={actionButtonStyles}
        >
          Close
        </Button>
        {!isEditing && processorStrId && (
          <Button
            onClick={handleEdit}
            variant="outlined"
            disabled={uploading || saving}
            sx={actionButtonStyles}
          >
            Edit
          </Button>
        )}
        {isEditing && (
          <Button
            onClick={handleSave}
            color="primary"
            variant="contained"
            disabled={uploading || saving}
            endIcon={saving ? <CircularProgress size={20} /> : null}
            sx={saveButtonStyles}
          >
            {saving ? 'Saving...' : 'Save'}
          </Button>
        )}
      </DialogActions>

      <Dialog
        open={!!previewImage}
        onClose={closePreview}
        maxWidth="lg"
        fullWidth
        sx={previewDialogStyles}
      >
        <Backdrop
          open={!!previewImage}
          onClick={closePreview}
          sx={backdropStyles}
        >
          <Box sx={previewContainerStyles}>
            <img
              src={previewImage || ''}
              alt="Preview"
              style={previewImageStyles}
              onClick={(e) => e.stopPropagation()}
              onKeyUp={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.stopPropagation();
                }
              }}
            />
            <IconButton onClick={closePreview} sx={previewCloseButtonStyles}>
              <Close />
            </IconButton>
          </Box>
        </Backdrop>
      </Dialog>
    </Dialog>
  );
};

interface DescriptionButtonProps {
  onClick: () => void;
  hasDescription?: boolean;
}

const DescriptionButton = ({
  onClick,
  hasDescription,
}: DescriptionButtonProps) => {
  return (
    <Tooltip title="Show description">
      <IconButton
        onClick={onClick}
        size="small"
        sx={{
          ...(descriptionButtonStyles as object),
          ...(getDescriptionButtonColor(hasDescription || false) as object),
        }}
      >
        <Description fontSize="small" />
      </IconButton>
    </Tooltip>
  );
};

export { ProcessorDescriptionModal, DescriptionButton };
