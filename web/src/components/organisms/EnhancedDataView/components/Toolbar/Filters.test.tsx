import { render, screen, fireEvent } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { vi } from 'vitest';

import {
  mockSetIsFilterScrollable,
  setIsFilterScrollable,
} from '../../__mocks__/store';
import { Filters } from './Filters';

vi.mock('@mui/icons-material/ChevronLeft', () => ({
  default: () => <span>ChevronLeftIcon</span>,
}));
vi.mock('@mui/icons-material/ChevronRight', () => ({
  default: () => <span>ChevronRightIcon</span>,
}));

// Mock child components
vi.mock('./FilterQueryChips', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  FilterQueryChips: ({ queryChips }: any) => {
    return (
      <div data-testid="filter-query-chips">{JSON.stringify(queryChips)}</div>
    );
  },
}));
vi.mock('./FilterDateRange', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  FilterDateRange: ({ dateFilters }: any) => (
    <div data-testid="filter-date-range">{JSON.stringify(dateFilters)}</div>
  ),
}));
vi.mock('./FilterNumberRange', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  FilterNumberRange: ({ numberRangeFilters }: any) => (
    <div data-testid="filter-number-range">
      {JSON.stringify(numberRangeFilters)}
    </div>
  ),
}));
vi.mock('./EnhancedDataViewFilterSelect', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  EnhancedDataViewFilterSelect: ({ filters }: any) => (
    <div data-testid="enhanced-data-view-filter-select">
      {JSON.stringify(filters)}
    </div>
  ),
}));

describe('Filters', () => {
  const defaultProps = {
    queryChips: [{ label: 'chip1' }],
    table: 'testTable',
    dateFilters: [{ id: 'date1' }],
    filters: [{ id: 'filter1' }],
    numberRangeFilters: [{ startKey: 'start', endKey: 'end' }],
    enableResetFilters: true,
    sortFilterByPosition: false,
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  } as any;

  const renderWithRouter = (component: React.ReactElement) => {
    return render(<MemoryRouter>{component}</MemoryRouter>);
  };

  beforeEach(() => {
    setIsFilterScrollable(false);
    mockSetIsFilterScrollable.mockClear();
  });

  it('Given FilterQueryChips, FilterDateRange, and EnhancedDataViewFilterSelect, should render them', () => {
    renderWithRouter(<Filters {...defaultProps} />);
    expect(screen.getByTestId('filter-query-chips')).toBeInTheDocument();
    expect(screen.getByTestId('filter-date-range')).toBeInTheDocument();
    expect(
      screen.getByTestId('enhanced-data-view-filter-select')
    ).toBeInTheDocument();
  });

  it('Given FilterNumberRange, should render it when numberRangeFilters provided', () => {
    renderWithRouter(<Filters {...defaultProps} />);
    expect(screen.getByTestId('filter-number-range')).toBeInTheDocument();
  });

  it('Given navigation icons, should render left and right icons', () => {
    renderWithRouter(<Filters {...defaultProps} />);
    expect(screen.getAllByText(/ChevronLeftIcon/)).toHaveLength(1);
    expect(screen.getAllByText(/ChevronRightIcon/)).toHaveLength(1);
  });

  it('Given navigation icons are clicked, should call scrollFilter', async () => {
    renderWithRouter(<Filters {...defaultProps} />);
    const buttons = await screen.findAllByRole('button', { hidden: true });
    const leftButton = buttons[0];
    const rightButton = buttons[1];

    // Mock scrollTo on the filter container
    const scrollToMock = vi.fn();
    Object.defineProperty(HTMLElement.prototype, 'scrollTo', {
      value: scrollToMock,
      writable: true,
    });

    fireEvent.click(leftButton);
    fireEvent.click(rightButton);

    // ScrollTo should be called twice (left and right)
    expect(scrollToMock).toHaveBeenCalledTimes(2);
  });

  it('Given enableResetFilters and sortFilterByPosition, should pass them to EnhancedDataViewFilterSelect', () => {
    renderWithRouter(<Filters {...defaultProps} />);
    expect(
      screen.getByTestId('enhanced-data-view-filter-select').textContent
    ).toContain('filter1');
  });

  it('Given scroll event, should call setIsFilterScrollable', () => {
    renderWithRouter(<Filters {...defaultProps} />);
    // Simulate scroll event
    const filterContainer = document.querySelector('.hiddenScrollbar');
    if (filterContainer) {
      fireEvent.scroll(filterContainer);
      expect(mockSetIsFilterScrollable).toHaveBeenCalled();
    }
  });
});
