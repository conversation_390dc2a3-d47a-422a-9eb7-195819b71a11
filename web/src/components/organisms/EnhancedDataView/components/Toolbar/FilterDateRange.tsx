import { Box, Tooltip } from '@mui/material';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import { useMemo } from 'react';

import { useIsMobile } from '../../hooks/useIsMobile';
import { useEnhancedDataViewStore } from '../../store';
import { FieldTypes } from '@/types';
import BasicDateRangePicker from '@/common/BasicDateRangePicker';
import { useSearchParamsUrl } from '../../hooks/useParams';
import { formatSearchDateParams } from '@/services/helpers';
import type { DataSpec } from '../../types';
import MoreDateFilters from '@/components/molecules/MoreDateFilters';

dayjs.extend(utc);

export const FilterDateRange = ({
  table,
  dateFilters,
}: Pick<DataSpec, 'table' | 'dateFilters'>) => {
  const { isMobile } = useIsMobile();
  const { searchParams, setSearchParams } = useSearchParamsUrl();
  const filterList = useEnhancedDataViewStore((s) => s.filterList);
  const invalidValues = [null, undefined, '', 'Invalid Date'];

  const dateRangeFilters = useMemo(() => {
    const dateFilters = filterList.filter(
      (f) => f.type === FieldTypes.DATE && f.id.includes('start')
    );
    return dateFilters
      .map(({ id: startKey, value: startValue, label: startLabel }) => {
        const endDateKey = startKey.replace('start', 'end');
        const { value: endDateValue, label: endDateLabel } =
          filterList.find((f) => f.id === endDateKey) || {};

        if (!endDateValue) return null;

        return (
          <BasicDateRangePicker
            key={startKey}
            range={{
              startDate: searchParams.get(startKey) || startValue || null,
              startDateLabel: startLabel,
              toolTipStartDate: (
                <Tooltip
                  key={startLabel}
                  title={
                    ['report_data', 'reconciliation_data'].includes(table)
                      ? 'Effective date'
                      : table === 'statement_data'
                        ? 'Payment date'
                        : ''
                  }
                  placement="top"
                >
                  <div></div>
                </Tooltip>
              ),
              endDate: searchParams.get(endDateKey) || endDateValue || null,
              endDateLabel: endDateLabel,
              toolTipEndDate: (
                <Tooltip
                  key={endDateLabel}
                  title={
                    ['report_data', 'reconciliation_data'].includes(table)
                      ? 'Effective date'
                      : table === 'statement_data'
                        ? 'Payment date'
                        : ''
                  }
                  placement="top"
                >
                  <div></div>
                </Tooltip>
              ),
            }}
            onChange={({ startDate, endDate }) => {
              setSearchParams((prev) => {
                if (startDate === 'Invalid Date') {
                  prev.delete(startKey);
                } else {
                  prev.set(
                    startKey,
                    startDate
                      ? dayjs.isDayjs(startDate)
                        ? startDate.toString()
                        : formatSearchDateParams(startDate)
                      : null
                  );
                }
                if (endDate === 'Invalid Date') {
                  prev.delete(endDateKey);
                } else {
                  prev.set(
                    endDateKey,
                    endDate
                      ? dayjs.isDayjs(endDate)
                        ? endDate.toString()
                        : formatSearchDateParams(endDate)
                      : null
                  );
                }

                return prev;
              });
            }}
          />
        );
      })
      .filter(Boolean);
  }, [filterList, searchParams, table, setSearchParams]);

  return (
    <Box
      sx={{
        mt: 1,
        mr: 1,
        mb: isMobile ? 0 : 1,
        display: 'flex',
        flexWrap: isMobile ? 'wrap' : 'nowrap',
        alignItems: 'center',
      }}
    >
      {dateRangeFilters}
      {!!dateFilters?.length && (
        <MoreDateFilters
          title="Additional date filters"
          filters={dateFilters ?? []}
          values={searchParams}
          onSetValue={(k, e) => {
            setSearchParams((prev) => {
              if (invalidValues.includes(e)) prev.delete(k);
              else prev.set(k, e);
              return prev;
            });
          }}
        />
      )}
    </Box>
  );
};
