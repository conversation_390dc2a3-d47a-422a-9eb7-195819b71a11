import type { BasicDateRangePickerProps } from '@/common/BasicDateRangePicker';
import type { MoreDateFiltersProps } from '@/components/molecules/MoreDateFilters';
import { render, screen } from '@testing-library/react';
import type { Dayjs } from 'dayjs';
import { afterEach, beforeEach, vi } from 'vitest';

import { FilterDateRange } from './FilterDateRange';
import type { DataSpec } from '../../types';
import { FieldTypes } from '@/types';

const { useIsMobileMock } = vi.hoisted(() => {
  return { useIsMobileMock: vi.fn() };
});

// Mock dependencies
vi.mock('../../hooks/useIsMobile', () => ({
  useIsMobile: useIsMobileMock,
}));

vi.mock('../../hooks/useParams', () => ({
  useSearchParamsUrl: () => ({
    searchParams: new Map(),
    setSearchParams: vi.fn(),
  }),
}));

vi.mock('../../store', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEnhancedDataViewStore: (fn: any) =>
    fn({
      filterList: [
        {
          id: 'date_start',
          type: 'date',
          value: '2024-01-01',
          label: 'Start Date',
        },
        {
          id: 'date_end',
          type: 'date',
          value: '2024-01-31',
          label: 'End Date',
        },
      ],
    }),
}));

vi.mock('@/common/BasicDateRangePicker', () => ({
  default: (props: BasicDateRangePickerProps) => (
    // biome-ignore lint/a11y/noStaticElementInteractions: No need for tests
    // biome-ignore lint/a11y/useKeyWithClickEvents: No need for tests
    <div
      data-testid="date-range-picker"
      onClick={() =>
        props.onChange({ startDate: '2024-01-01', endDate: '2024-01-31' })
      }
    >
      DateRangePickerMock
    </div>
  ),
}));

vi.mock('@/components/molecules/MoreDateFilters', () => ({
  default: (props: MoreDateFiltersProps) => (
    <div data-testid="more-date-filters">{props.title}</div>
  ),
}));

vi.mock('@/services/helpers', () => ({
  formatSearchDateParams: (date: string) => date,
}));

vi.mock('dayjs', async (importActual) => {
  const actual = await importActual<
    typeof import('dayjs') & { default: (arg: unknown) => Dayjs }
  >();
  const dayjsMock = (date?: unknown) => actual.default(date);
  Object.assign(dayjsMock, actual.default, { isDayjs: () => false });
  return {
    ...actual,
    default: dayjsMock,
  };
});

describe('FilterDateRange', () => {
  const defaultProps: Pick<DataSpec, 'table' | 'dateFilters'> = {
    table: 'report_data',
    dateFilters: [
      {
        filterFieldId: 'processing_date',
        filterFieldName: 'Processing date',
        filters: [
          {
            label: 'Processing date',
            filterKey: 'processing_date',
            type: FieldTypes.DATE,
          },
        ],
      },
    ],
  };

  beforeEach(() => {
    useIsMobileMock.mockReturnValue({ isMobile: false });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('Given date range picker, should render it', () => {
    render(<FilterDateRange {...defaultProps} />);
    expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
  });

  it('Given dateFilters are provided, should render MoreDateFilters', () => {
    render(<FilterDateRange {...defaultProps} />);
    expect(screen.getByTestId('more-date-filters')).toHaveTextContent(
      'Additional date filters'
    );
  });

  it('Given isMobile is true, should render with mobile styles', () => {
    useIsMobileMock.mockReturnValue({ isMobile: true });
    render(<FilterDateRange {...defaultProps} />);
    // Not directly testable without style assertion, but should not throw
  });

  it('Given empty dateFilters, should not render MoreDateFilters', () => {
    render(<FilterDateRange table="report_data" dateFilters={[]} />);
    expect(screen.queryByTestId('more-date-filters')).not.toBeInTheDocument();
  });
});
