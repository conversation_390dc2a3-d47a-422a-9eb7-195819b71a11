import { render, screen, fireEvent } from '@testing-library/react';
import { vi, type Mock } from 'vitest';

import { FilterSelect } from './FilterSelect';
import { useSearchParamsUrl } from '../../hooks/useParams';

vi.mock('@/components/molecules/EnhancedSelect', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  EnhancedSelect: ({ label, ...props }: any) => (
    // biome-ignore lint/a11y/noLabelWithoutControl: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    // biome-ignore lint/a11y/useValidAriaRole: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    <label role="label" aria-label={label} {...props}>
      {label}
    </label>
  ),
}));

// Mock dependencies
vi.mock('../../hooks/useIsMobile', () => ({
  useIsMobile: () => ({ isMobile: false }),
}));
vi.mock('../../hooks/useParams', () => ({
  useSearchParamsUrl: vi.fn(() => ({
    searchParams: new URLSearchParams(),
    setSearchParams: vi.fn(),
  })),
}));

vi.mock('../../store', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEnhancedDataViewStore: (fn: any) =>
    fn({
      filterList: [
        {
          id: 'category',
          label: 'Category',
          type: 'multiSelect',
          options: [
            { id: 'a', label: 'A' },
            { id: 'b', label: 'B' },
          ],
        },
        {
          id: 'contacts',
          label: 'Contacts',
          type: 'multiSelect',
          options: [
            { id: '1', label: 'John' },
            { id: '2', label: 'Jane' },
          ],
        },
        {
          id: 'status',
          label: 'Status',
          type: 'multiSelect',
          options: ['active', 'inactive'],
        },
      ],
    }),
}));

describe('FilterSelect', () => {
  const filters = {
    category: { label: 'Category' },
    contacts: { label: 'Contacts' },
    status: { label: 'Status' },
  };
  beforeEach(() => {
    vi.clearAllMocks();
    (useSearchParamsUrl as Mock).mockReturnValue({
      searchParams: new URLSearchParams(),
      setSearchParams: vi.fn(),
    });
  });

  it('Given filters with object and string options, should render filter selects', () => {
    render(
      <FilterSelect
        filters={filters}
        sortFilterByPosition={false}
        enableResetFilters={false}
      />
    );
    expect(screen.getByLabelText('Category')).toBeInTheDocument();
    expect(screen.getByLabelText('Contacts')).toBeInTheDocument();
    expect(screen.getByLabelText('Status')).toBeInTheDocument();
  });

  it('Given filters and params exist, should show reset button', () => {
    const searchParams = new URLSearchParams('category=a');
    Object.defineProperties(searchParams, {
      size: {
        get: () => 1,
      },
    });
    (useSearchParamsUrl as Mock).mockReturnValue({
      searchParams,
      setSearchParams: vi.fn(),
    });
    render(
      <FilterSelect
        filters={filters}
        sortFilterByPosition={false}
        enableResetFilters={true}
      />
    );

    expect(screen.getByTestId('btn-clear-filters')).toBeInTheDocument();
  });

  it('Given clear filters button is clicked, should call setSearchParams', () => {
    const setSearchParams = vi.fn();
    const searchParams = new URLSearchParams('category=a');
    Object.defineProperties(searchParams, {
      size: {
        get: () => 1,
      },
    });
    (useSearchParamsUrl as Mock).mockReturnValue({
      searchParams,
      setSearchParams,
    });
    render(
      <FilterSelect
        filters={filters}
        sortFilterByPosition={false}
        enableResetFilters={true}
      />
    );
    fireEvent.click(screen.getByRole('button', { name: /clear filters/i }));
    expect(setSearchParams).toHaveBeenCalledWith({});
  });

  it('Given default sorting, should sort filters by label', () => {
    render(
      <FilterSelect
        filters={filters}
        sortFilterByPosition={false}
        enableResetFilters={false}
      />
    );
    const selects = screen.getAllByRole('label');
    expect(selects[0].textContent).toContain('Category');
    expect(selects[1].textContent).toContain('Contacts');
    expect(selects[2].textContent).toContain('Status');
  });

  it('Given sortFilterByPosition is enabled, should sort filters by sortPosition', () => {
    const filtersWithSort = {
      ...filters,
      contacts: { ...filters.contacts, sortPosition: 1 },
      category: { ...filters.category, sortPosition: 2 },
      status: { ...filters.status, sortPosition: 3 },
    };
    render(
      <FilterSelect
        filters={filtersWithSort}
        sortFilterByPosition={true}
        enableResetFilters={false}
      />
    );
    const selects = screen.getAllByRole('label');
    expect(selects[0].textContent).toContain('Contacts');
    expect(selects[1].textContent).toContain('Category');
    expect(selects[2].textContent).toContain('Status');
  });
});
