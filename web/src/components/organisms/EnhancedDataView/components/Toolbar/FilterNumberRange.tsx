import { <PERSON>, TextField, Tooltip } from '@mui/material';
import { isNill } from 'common/helpers';
import { NumberParam, useQueryParam } from 'use-query-params';
import { useState } from 'react';
import { useDebouncedCallback } from 'use-debounce';

export const FilterNumberRange = ({
  startKey,
  endKey,
  startLabel,
  endLabel,
  min,
  max,
  step = 0.01,
}: {
  startKey: string;
  endKey: string;
  startLabel?: string;
  endLabel?: string;
  min?: number;
  max?: number;
  step?: number;
}) => {
  const [startValueParam, setStartValueParam] = useQueryParam(
    startKey,
    NumberParam
  );
  const [endValueParam, setEndValueParam] = useQueryParam(endKey, NumberParam);

  const [startValue, setStartValue] = useState(startValueParam);
  const [endValue, setEndValue] = useState(endValueParam);

  const debounceUpdateStartParam = useDebouncedCallback(
    (updatedStart: number | undefined | null) =>
      setStartValueParam(updatedStart),
    250
  );

  const debounceUpdateEndParam = useDebouncedCallback(
    (updatedEnd: number | undefined | null) => setEndValueParam(updatedEnd),
    250
  );

  const handleUpdateStart = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = isNill(e.target.value) ? undefined : +e.target.value;
    setStartValue(newValue);
    debounceUpdateStartParam(newValue);
  };

  const handleUpdateEnd = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = isNill(e.target.value) ? undefined : +e.target.value;
    setEndValue(newValue);
    debounceUpdateEndParam(newValue);
  };

  return (
    <Box sx={{ display: 'flex', alignItems: 'center' }}>
      <Tooltip title={startLabel || 'Min'} placement="top">
        <TextField
          size="small"
          type="number"
          slotProps={{
            htmlInput: { min, max, step },
            inputLabel: { shrink: true },
          }}
          label={startLabel || 'Min'}
          value={startValue}
          onChange={handleUpdateStart}
          sx={{
            width: 120,
            '& input': {
              padding: '6px 12px',
            },
            '& .MuiInputBase-root': {
              borderTopRightRadius: 0,
              borderBottomRightRadius: 0,
            },
          }}
        />
      </Tooltip>
      <Tooltip title={endLabel || 'Max'} placement="top">
        <TextField
          size="small"
          type="number"
          slotProps={{
            htmlInput: { min, max, step },
            inputLabel: { shrink: true },
          }}
          label={endLabel || 'Max'}
          value={endValue}
          onChange={handleUpdateEnd}
          sx={{
            width: 120,
            '& input': {
              padding: '6px 12px',
            },
            '& .MuiInputBase-root': {
              borderTopLeftRadius: 0,
              borderBottomLeftRadius: 0,
            },
          }}
        />
      </Tooltip>
    </Box>
  );
};
