import UIStateProvider from '@/contexts/UIStateProvider';
import { fireEvent, render, screen } from '@testing-library/react';
import { SystemRoles } from 'common/globalTypes';

import { setSearchParams } from '../../hooks/__mocks__/useParams';
import { useUserInfo } from '@/hooks/__mocks__/useUserInfo';
import { FilterQueryChips } from './FilterQueryChips';

describe('FilterQueryChips', () => {
  beforeEach(() => {
    useUserInfo.mockReturnValue({
      data: { fintaryAdmin: { email: '' } },
    });
  });
  const baseChips = {
    all: { id: 'all', label: 'All', access: undefined, query: undefined },
    foo: {
      id: 'foo',
      label: 'Foo',
      access: undefined,
      testId: 'foo',
      query: undefined,
    },
    bar: { id: 'bar', label: 'Bar', access: undefined, query: undefined },
  };

  it('Given no chips, should render nothing', () => {
    const { container } = render(<FilterQueryChips queryChips={{}} />);
    expect(container.firstChild).toBeNull();
  });

  it('Given chips, should render all visible chips', () => {
    render(<FilterQueryChips queryChips={baseChips} />);
    expect(screen.getByText('All')).toBeInTheDocument();
    expect(screen.getByText('Foo')).toBeInTheDocument();
    expect(screen.getByText('Bar')).toBeInTheDocument();
  });

  it('Given admin chip and user is admin, should show admin chip with lock', () => {
    const chips = {
      ...baseChips,
      admin: {
        id: 'admin',
        label: 'Admin',
        access: SystemRoles.ADMIN,
        query: undefined,
      },
    };
    useUserInfo.mockReturnValue({
      data: { fintaryAdmin: { email: '<EMAIL>' } },
    });
    render(<FilterQueryChips queryChips={chips} />);
    screen.debug();
    expect(screen.getByText('Admin 🔒')).toBeInTheDocument();
  });

  it('Given admin chip and user is not admin, should not show admin chip', () => {
    vi.mock('@/hooks/useUserInfo', () => ({
      useUserInfo: () => ({ data: { fintaryAdmin: false } }),
    }));
    const chips = {
      ...baseChips,
      admin: { id: 'admin', label: 'Admin', access: 'ADMIN', query: undefined },
    };
    render(<FilterQueryChips queryChips={chips} />);
    expect(screen.queryByText('Admin 🔒')).not.toBeInTheDocument();
  });

  it('Given chip with more=true, should render MoreMenu', () => {
    const chips = {
      ...baseChips,
      active: {
        id: 'active',
        label: 'Active',
        access: undefined,
        query: undefined,
        more: {
          id: 'more',
          label: 'More',
          access: undefined,
          more: true,
        },
      },
    };
    render(
      <UIStateProvider>
        <FilterQueryChips queryChips={chips} />
      </UIStateProvider>
    );
    expect(screen.getByTestId('MoreVertIcon')).toBeInTheDocument();
  });

  it('Given chip is clicked, should call setSearchParams', async () => {
    render(<FilterQueryChips queryChips={baseChips} />);
    fireEvent.click(screen.getByTestId('foo'));
    expect(setSearchParams).toHaveBeenCalled();
  });
});
