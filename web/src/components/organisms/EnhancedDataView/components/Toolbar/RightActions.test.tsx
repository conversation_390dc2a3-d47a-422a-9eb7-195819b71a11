import { render, screen, fireEvent } from '@testing-library/react';
import { vi, type Mock } from 'vitest';

import { RightActions } from './RightActions';
import { useEnhancedDataViewStore } from '../../store';

// Mock useDisplayFields hook
vi.mock('../../hooks/useDisplayFields', () => ({
  useDisplayFields: vi.fn(),
}));

// Mocking these components to avoid unnecessary complexity in tests
vi.mock('@/common/BasicDateRangePicker', () => ({
  default: (props: Record<string, unknown>) => {
    return <div {...props}>DateRange</div>;
  },
}));

vi.mock('@/components/molecules/BasicDatePicker', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  default: ({ label, ...props }: any) => (
    // biome-ignore lint/a11y/noLabelWithoutControl: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    <label aria-label={label} {...props}>
      {label}
    </label>
  ),
}));
vi.mock('@/components/molecules/EnhancedSelect', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  EnhancedSelect: ({ label, onChange, value, options }: any) => {
    return (
      <select
        aria-label={label}
        multiple
        data-testid={label}
        value={value}
        onChange={(e) => {
          // Simulate multi-select value array
          const selected = Array.from(e.target.selectedOptions).map(
            (o) => o.value
          );
          onChange(selected);
        }}
      >
        {options.map((opt: string) => (
          <option key={opt} value={opt}>
            {opt}
          </option>
        ))}
      </select>
    );
  },
}));

vi.mock('@/components/molecules/MultiSelect', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  default: ({ label, ...props }: any) => (
    // biome-ignore lint/a11y/noLabelWithoutControl: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    <label aria-label={label} {...props}>
      {label}
    </label>
  ),
}));

vi.mock('@/components/molecules/SplitButton', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  default: ({ options }: any) =>
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    options.map((option: any) => (
      // biome-ignore lint/a11y/useButtonType: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
      <button key={option.label} onClick={option.onClick}>
        {option.label}
      </button>
    )),
}));

// Mock helpers
vi.mock('@/services/helpers', () => ({
  formatSearchDateParams: vi.fn((start, end) => ({
    start_date: start,
    end_date: end,
  })),
}));

// Mocks
vi.mock('../../store', () => ({
  useEnhancedDataViewStore: vi.fn(),
}));

vi.mock('../../hooks/useIsMobile', () => ({
  useIsMobile: () => ({ isMobile: false }),
}));
vi.mock('../../hooks/useParams', () => ({
  useSearchParamsUrl: () => ({
    searchParams: new URLSearchParams(),
    updateSearchParams: vi.fn(),
    setSearchParams: vi.fn(),
  }),
}));
vi.mock('../../hooks/useDownloadCsv', () => ({
  useDownloadCsv: () => ({
    handleDownload: vi.fn(),
    isDownloading: false,
  }),
}));
vi.mock('../../hooks/useAddGlobalCompanyButton', () => ({
  useAddGlobalCompanyButton: vi.fn(),
}));
vi.mock('./Bookmark', () => ({
  Bookmark: () => <div data-testid="bookmark" />,
}));

const defaultProps = {
  dataSpec: {
    fields: {
      name: { label: 'Name', defaultTableHidden: false, type: 'string' },
      age: { label: 'Age', defaultTableHidden: false, type: 'number' },
      secret: { label: '🔒Secret', defaultTableHidden: false, type: 'string' },
      dynamic: {
        label: 'Dynamic',
        defaultTableHidden: true,
        type: 'dynamic-select',
      },
    },
    queryChips: [],
    table: 'test_table',
  },
  setNewData: vi.fn(),
  bulkAdd: true,
  hideExport: false,
  enableSaves: true,
  extraActions: [],
  exportOptions: [],
  enableBulkEditCsv: true,
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
} as any;

const defaultStore = {
  addGlobalCompanyConfig: {
    hideAddSelect: false,
    addBtnLabel: 'Add',
  },
  isFilterScrollable: false,
  setDisplayFields: vi.fn(),
  displayFields: [],
};

describe('RightActions', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (useEnhancedDataViewStore as any as Mock).mockImplementation((s) =>
      s ? s(defaultStore) : defaultStore
    );
  });

  it('Given default state, should render fields select and add button', () => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (useEnhancedDataViewStore as any as Mock).mockReturnValue({
      hideAddSelect: false,
      addBtnLabel: 'Add',
      isFilterScrollable: false,
      setDisplayFields: vi.fn(),
      displayFields: [],
    });
    render(<RightActions {...defaultProps} />);
    expect(screen.getByLabelText('Fields')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Add' })).toBeInTheDocument();
  });

  it('Given hideAddSelect is true, should not render add button', () => {
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
    (useEnhancedDataViewStore as any as Mock).mockReturnValue({
      hideAddSelect: true,
      addBtnLabel: 'Add',
      isFilterScrollable: false,
    });
    render(<RightActions {...defaultProps} />);
    expect(
      screen.queryByRole('button', { name: 'Add' })
    ).not.toBeInTheDocument();
  });

  it('Given default state, should render export button', () => {
    render(<RightActions {...defaultProps} />);
    expect(screen.getByText('Export')).toBeInTheDocument();
    expect(screen.getByText('Export with IDs')).toBeInTheDocument();
  });

  it('Given enableSaves is true, should render bookmark', () => {
    render(<RightActions {...defaultProps} />);
    expect(screen.getByTestId('bookmark')).toBeInTheDocument();
  });

  it('Given extraActions of type button, should render as button', () => {
    render(
      <RightActions
        {...defaultProps}
        extraActions={[
          {
            type: 'button',
            label: 'ExtraBtn',
            onClick: vi.fn(),
            icon: <span>icon</span>,
          },
        ]}
      />
    );
    expect(screen.getByText('ExtraBtn')).toBeInTheDocument();
  });

  it('Given extraActions of type select, should render as select', () => {
    render(
      <RightActions
        {...defaultProps}
        extraActions={[
          {
            type: 'select',
            label: 'ExtraSelect',
            value: '1',
            onChange: vi.fn(),
            options: [{ value: '1', label: 'One' }],
          },
        ]}
      />
    );
    expect(screen.getByText('One')).toBeInTheDocument();
  });

  it('Given extraActions of type date, should render as date', () => {
    render(
      <RightActions
        {...defaultProps}
        extraActions={[
          {
            type: 'date',
            label: 'Date',
            value: null,
            onChange: vi.fn(),
          },
        ]}
      />
    );
    expect(screen.getByLabelText('Date')).toBeInTheDocument();
  });

  it('Given extraActions of type dateRange, should render as dateRange', () => {
    render(
      <RightActions
        {...defaultProps}
        extraActions={[
          {
            type: 'dateRange',
            label: 'DateRange',
            value: null,
            onChange: vi.fn(),
          },
        ]}
      />
    );
    expect(screen.getByText('DateRange')).toBeInTheDocument();
  });

  it('Given extraActions of type multiSelect, should render as multiSelect', () => {
    render(
      <RightActions
        {...defaultProps}
        extraActions={[
          {
            type: 'multiSelect',
            label: 'MultiSelect',
            value: [],
            options: [{ value: 'a', label: 'A' }],
            onChange: vi.fn(),
          },
        ]}
      />
    );
    expect(screen.getByLabelText('MultiSelect')).toBeInTheDocument();
  });

  it('Given hideExport is true, should not render export button', () => {
    render(<RightActions {...defaultProps} hideExport={true} />);
    expect(screen.queryByText(/Export/i)).not.toBeInTheDocument();
  });

  it('Given fields select changes, should call setFieldsStorage', () => {
    render(<RightActions {...defaultProps} />);
    const select = screen.getByTestId('Fields');
    fireEvent.change(select, { target: { value: ['Name'] } });
    expect(localStorage.getItem(`ui${window.location.pathname}`)).toContain(
      'Name'
    );
  });
});
