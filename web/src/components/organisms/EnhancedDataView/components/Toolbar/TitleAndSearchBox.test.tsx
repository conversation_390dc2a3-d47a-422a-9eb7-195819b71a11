import { render, screen } from '@testing-library/react';
import { vi, type Mock } from 'vitest';

import { TitleAndSearchBox } from './TitleAndSearchBox';
import { useIsMobile } from '../../hooks/useIsMobile';
import { useSearchSettings } from '../../hooks/useSearchSettings';

vi.mock('../../hooks/useIsMobile', () => {
  return {
    useIsMobile: vi.fn(),
  };
});

vi.mock('../../hooks/useSearchSettings', () => {
  return {
    useSearchSettings: vi.fn(),
  };
});

// Mock child components
vi.mock('@/components/molecules/SearchBox', () => ({
  SearchBoxUrlQuery: (props: Record<string, unknown>) => (
    <div data-testid="search-box" {...props} />
  ),
}));
vi.mock('@/components/molecules/SearchSettings', () => ({
  default: (props: Record<string, unknown>) => (
    <div data-testid="search-settings" {...props} />
  ),
}));

describe('TitleAndSearchBox', () => {
  const baseProps = {
    label: 'Test Label',
    variant: 'default',
    table: 'test_table',
    // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  } as any;

  beforeEach(() => {
    vi.clearAllMocks();
    (useIsMobile as Mock).mockReturnValue({ isMobile: false });
    (useSearchSettings as Mock).mockReturnValue({ searchSettings: [] });
  });

  it('Given default props, should render label and SearchBox', () => {
    render(<TitleAndSearchBox {...baseProps} />);
    expect(screen.getByText('Test Label')).toBeInTheDocument();
    expect(screen.getByTestId('search-box')).toBeInTheDocument();
  });

  it('Given tabbed variant, should not render label', () => {
    render(<TitleAndSearchBox {...baseProps} variant="tabbed" />);
    expect(screen.queryByText('Test Label')).not.toBeInTheDocument();
  });

  it('Given searchSettings is not empty, should render SearchSettings', () => {
    (useSearchSettings as Mock).mockReturnValue({
      searchSettings: [{ id: 1 }],
    });
    render(<TitleAndSearchBox {...baseProps} />);
    expect(screen.getByTestId('search-settings')).toBeInTheDocument();
  });

  it('Given isMobile true, should set flexWrap to wrap', () => {
    (useIsMobile as Mock).mockReturnValue({ isMobile: true });
    const { container } = render(<TitleAndSearchBox {...baseProps} />);
    const box = container.querySelector('.MuiBox-root');
    expect(box).toHaveStyle('flex-wrap: wrap');
  });

  it('Given isMobile false, should set flexWrap to nowrap', () => {
    (useIsMobile as Mock).mockReturnValue({ isMobile: false });
    const { container } = render(<TitleAndSearchBox {...baseProps} />);
    const box = container.querySelector('.MuiBox-root');
    expect(box).toHaveStyle('flex-wrap: nowrap');
  });
});
