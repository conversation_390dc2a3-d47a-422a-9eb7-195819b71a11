import { Box, Chip } from '@mui/material';
import { SystemRoles } from 'common/globalTypes';

import type { DataSpec } from '../../types';
import { useIsMobile } from '../../hooks/useIsMobile';
import MoreMenu from '@/components/molecules/MoreMenu';
import { useSearchParamsUrl } from '../../hooks/useParams';
import { useUserInfo } from '@/hooks/useUserInfo';
import { getTestId } from '@/utils/test';

export const FilterQueryChips = ({
  queryChips,
}: Pick<DataSpec, 'queryChips'>) => {
  const { isMobile } = useIsMobile();
  const { data: { fintaryAdmin } = {} } = useUserInfo();
  const { searchParams, setSearchParams } = useSearchParamsUrl();
  const chips = Object.keys(queryChips || {});
  if (chips.length === 0) return null;

  return (
    <Box
      sx={{
        whiteSpace: 'wrap',
        display: 'flex',
        width: isMobile ? '100%' : 'auto',
        justifyContent: 'start',
        alignItems: 'center',
        mb: isMobile ? 1.5 : 0,
      }}
    >
      {Object.entries(queryChips ?? {})
        .filter(([_k, v]) => !v.more)
        .filter(([_k, v]) => v.access !== SystemRoles.ADMIN || fintaryAdmin)
        .map(([_k, chip]) => {
          return (
            <Chip
              label={`${chip.label}${chip.access === SystemRoles.ADMIN ? ' 🔒' : ''}`}
              key={chip.id}
              {...getTestId(chip.testId)}
              onClick={() => {
                setSearchParams((prev) => {
                  if (chip.id === 'all') {
                    prev.delete('qc');
                  } else {
                    prev.set('qc', chip.id);
                  }
                  prev.delete('m');
                  return prev;
                });
              }}
              sx={{ mr: 0.5, cursor: 'pointer' }}
              color={
                searchParams.get('qc') === chip.id ||
                (!searchParams.get('qc') && chip.id === 'all')
                  ? 'primary'
                  : 'default'
              }
              variant={
                searchParams.get('qc') === chip.id ||
                (!searchParams.get('qc') && chip.id === 'all')
                  ? 'filled'
                  : 'outlined'
              }
            />
          );
        })}
      {Object.values(queryChips ?? {}).filter((chip) => chip.more).length >
        0 && (
        <MoreMenu
          actions={Object.values(queryChips ?? {})
            .filter((chip) => chip.more)
            ?.map((chip) => ({
              label: chip.label,
              onClick: () => {
                setSearchParams((prev) => {
                  if (chip.id === 'all') {
                    prev.delete('qc');
                  } else {
                    prev.set('qc', chip.id);
                  }
                  return prev;
                });
              },
            }))}
          data={null}
          // biome-ignore lint/suspicious/noEmptyBlockStatements: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
          setActionLoading={() => {}}
          sx={{ mr: 1 }}
        />
      )}
    </Box>
  );
};
