import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi } from 'vitest';

import { mutateAsyncMock } from '@/services/__mocks__/API';
import { showSnackbarMock } from '@/contexts/__mocks__/useSnackbar';
import { Bookmark } from './Bookmark';

vi.mock('@/firebase', () => ({
  auth: vi.fn(() => ({
    currentUser: null,
    signInWithEmailAndPassword: vi.fn(),
    createUserWithEmailAndPassword: vi.fn(),
    signOut: vi.fn(),
    onAuthStateChanged: vi.fn(),
  })),
}));

const getSaveButton = async () => screen.findByTestId('save-button');
const getBookmarkButton = () => screen.getByTestId('bookmark-button');
const getTextField = async () => screen.findByRole('textbox');

// Mock window.location.search
beforeEach(() => {
  window.history.pushState({}, '', '?foo=bar');
});
afterEach(() => {
  window.history.pushState({}, '', '/');
});

describe('Bookmark', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('Given bookmark icon button, should render it', () => {
    render(<Bookmark table="statement_data" />);
    expect(screen.getAllByTestId('bookmark-icon').length).toBeGreaterThan(0);
  });

  it('Given params exist, should show tooltip with correct message', async () => {
    window.location.search = '?foo=bar';
    render(<Bookmark table="statement_data" />);
    const iconButton = getBookmarkButton();
    await userEvent.hover(iconButton);
    expect(await screen.findByText(/save current view/i)).toBeInTheDocument();
  });

  it('Given params are empty, should show tooltip with correct message', async () => {
    window.history.pushState({}, '', '/');
    render(<Bookmark table="statement_data" />);
    const iconButton = getBookmarkButton();
    await userEvent.hover(iconButton);
    expect(
      await screen.findByText(/customize filters to save view/i)
    ).toBeInTheDocument();
    // Restore
    window.history.pushState({}, '', '?foo=bar');
  });

  it('Given popover is opened, should allow entering view name', async () => {
    render(<Bookmark table="statement_data" />);
    fireEvent.click(getBookmarkButton());
    expect(await screen.findByTestId('popover')).toBeInTheDocument();
    const input = await getTextField();
    fireEvent.change(input, { target: { value: 'My View' } });
    expect(input).toHaveValue('My View');
  });

  it('Given onSave is called, should show snackbar on success', async () => {
    mutateAsyncMock.mockResolvedValueOnce({});
    render(<Bookmark table="statement_data" />);
    fireEvent.click(getBookmarkButton());
    const input = await getTextField();
    fireEvent.change(input, { target: { value: 'Test View' } });
    fireEvent.click(await getSaveButton());

    await waitFor(() => {
      expect(showSnackbarMock).toHaveBeenCalledWith('Saving view...');
      expect(mutateAsyncMock).toHaveBeenCalledWith({
        page: 'commissions',
        params: '?foo=bar',
        name: 'Test View',
        type: 'view',
      });
      expect(showSnackbarMock).toHaveBeenCalledWith('View saved', 'success');
    });
  });

  it('Given onSave is called, should show snackbar on error', async () => {
    mutateAsyncMock.mockResolvedValueOnce({ error: 'Failed' });
    render(<Bookmark table="statement_data" />);
    fireEvent.click(getBookmarkButton());
    fireEvent.change(await getTextField(), {
      target: { value: 'Test View' },
    });
    fireEvent.click(await getSaveButton());
    await waitFor(() => {
      expect(showSnackbarMock).toHaveBeenCalledWith('Saving view...');
      expect(showSnackbarMock).toHaveBeenCalledWith('Failed', 'error');
    });
  });

  it('Given table prop, should compute correct page', async () => {
    render(<Bookmark table="report_data" />);
    fireEvent.click(getBookmarkButton());
    fireEvent.change(await getTextField(), {
      target: { value: 'Another View' },
    });
    mutateAsyncMock.mockResolvedValueOnce({});
    fireEvent.click(await getSaveButton());
    await waitFor(() => {
      expect(mutateAsyncMock).toHaveBeenCalledWith(
        expect.objectContaining({ page: 'policies' })
      );
    });
  });

  it('Given unknown table, should compute empty page', async () => {
    render(<Bookmark table="unknown_table" />);
    fireEvent.click(getBookmarkButton());
    fireEvent.change(await getTextField(), {
      target: { value: 'Unknown View' },
    });
    mutateAsyncMock.mockResolvedValueOnce({});
    fireEvent.click(await getSaveButton());
    await waitFor(() => {
      expect(mutateAsyncMock).toHaveBeenCalledWith(
        expect.objectContaining({ page: '' })
      );
    });
  });
});
