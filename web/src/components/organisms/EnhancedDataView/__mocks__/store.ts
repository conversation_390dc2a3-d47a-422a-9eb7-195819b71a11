import { vi } from 'vitest';

export const mockSetIsFilterScrollable = vi.fn();

let isFilterScrollableValue = false;
export const setIsFilterScrollable = (v: boolean) =>
  // biome-ignore lint/suspicious/noAssignInExpressions: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  (isFilterScrollableValue = v);

vi.doMock('../store', () => ({
  // biome-ignore lint/suspicious/noExplicitAny: Suppressed in initial biome linting check -- please fix these errors if you are modifying this file --INITIAL_FIX--
  useEnhancedDataViewStore: (selector: any) =>
    selector({
      isFilterScrollable: isFilterScrollableValue,
      setIsFilterScrollable: mockSetIsFilterScrollable,
    }),
}));
