import { useMemo, useState } from 'react';
import { Box, IconButton, Chip, Tooltip, Menu, MenuItem } from '@mui/material';
import { EnhancedSelect } from '../EnhancedSelect';
import { RemoveCircleOutline, SwapVert, FilterList } from '@mui/icons-material';
import type { SortBy } from 'common/dto/widgets';
import type { AvailableField } from './SortByManager';

interface SortBySelectorProps {
  fields: AvailableField[];
  selectedSortBy: SortBy;
  onRemove: () => void;
  onUpdate: (updatedSortBy: Partial<SortBy>) => void;
}

const ORDER_OPTIONS = [
  { id: null, label: 'Unspecified' },
  { id: 'asc', label: 'Ascending' },
  { id: 'desc', label: 'Descending' },
];

const LIMIT_OPTIONS = [
  { id: 5, label: '5' },
  { id: 10, label: '10' },
  { id: 15, label: '15' },
];

const SortBySelector = ({
  fields,
  selectedSortBy,
  onRemove,
  onUpdate,
}: SortBySelectorProps) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [limitAnchorEl, setLimitAnchorEl] = useState<null | HTMLElement>(null);

  const options = useMemo(
    () =>
      fields.map((field) => ({
        id: field.name,
        label: field.displayName,
      })),
    [fields]
  );

  const selectedFieldDef = fields.find(
    (field) => field.name === selectedSortBy.field
  );

  const getFieldLabel = () => {
    return selectedFieldDef?.displayName || selectedSortBy.field || '';
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLimitMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setLimitAnchorEl(event.currentTarget);
  };

  const handleLimitMenuClose = () => {
    setLimitAnchorEl(null);
  };

  const handleOrderChange = (id: 'asc' | 'desc' | null) => {
    onUpdate({ order: id });
    handleMenuClose();
  };

  const handleLimitChange = (id: number) => {
    onUpdate({ limit: id });
    handleLimitMenuClose();
  };

  const orderLabel =
    ORDER_OPTIONS.find((opt) => opt.id === selectedSortBy.order)?.label ||
    'Order';

  const limitValue =
    LIMIT_OPTIONS.find((opt) => opt.id === selectedSortBy.limit)?.label ||
    LIMIT_OPTIONS[0].label;

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
      className="mt-3"
    >
      <Box data-name="sort-by-field">
        <EnhancedSelect
          enableSearch
          label="Field"
          options={options}
          value={{
            id: selectedSortBy.field,
            label: getFieldLabel(),
          }}
          onChange={(value) => {
            onUpdate({ field: value.id });
          }}
          sx={{ width: '100%' }}
        />
      </Box>
      <Box ml={1} display={'flex'} alignItems={'center'}>
        <Chip
          sx={{ marginRight: 1 }}
          data-name="sort-by-order"
          label={orderLabel}
          avatar={<SwapVert />}
          onClick={handleMenuOpen}
        />
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          {ORDER_OPTIONS.map((option) => (
            <MenuItem
              key={option.id ?? 'unspecified'}
              selected={selectedSortBy.order === option.id}
              onClick={() =>
                handleOrderChange(option.id as 'asc' | 'desc' | null)
              }
            >
              {option.label}
            </MenuItem>
          ))}
        </Menu>
        <Tooltip title="Limit">
          <Chip
            sx={{ marginRight: 1 }}
            data-name="sort-by-limit"
            label={limitValue}
            avatar={<FilterList />}
            onClick={handleLimitMenuOpen}
          />
        </Tooltip>
        <Menu
          anchorEl={limitAnchorEl}
          open={Boolean(limitAnchorEl)}
          onClose={handleLimitMenuClose}
        >
          {LIMIT_OPTIONS.map((option) => (
            <MenuItem
              key={option.id}
              selected={selectedSortBy.limit === option.id}
              onClick={() => handleLimitChange(option.id)}
            >
              {option.label}
            </MenuItem>
          ))}
        </Menu>
      </Box>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'flex-end',
          gap: 1,
          width: '100%',
        }}
      >
        <IconButton onClick={onRemove}>
          <RemoveCircleOutline />
        </IconButton>
      </div>
    </div>
  );
};

export default SortBySelector;
