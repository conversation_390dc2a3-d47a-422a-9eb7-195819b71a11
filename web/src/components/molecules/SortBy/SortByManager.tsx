import { Button } from '@mui/material';

import SortBySelector from './SortBySelector';
import type { SortBy } from 'common/dto/widgets';

export type AvailableField = {
  name: string;
  displayName: string;
};

interface SortByManagerProps {
  fields: AvailableField[];
  sortBys: SortBy[];
  addSortBy: () => void;
  removeSortBy: (id: number) => void;
  updateSortBy: (
    id: number,
    updatedField: Partial<{ field: string; direction: 'asc' | 'desc' }>
  ) => void;
}

const SortByManager = ({
  fields,
  sortBys,
  addSortBy,
  removeSortBy,
  updateSortBy,
}: SortByManagerProps) => {
  return (
    <div style={{ width: '100%' }}>
      {sortBys.map((sortBy) => (
        <SortBySelector
          key={sortBy.id}
          fields={fields}
          selectedSortBy={sortBy}
          onRemove={() => removeSortBy(sortBy.id)}
          onUpdate={(updatedField) => updateSortBy(sortBy.id, updatedField)}
        />
      ))}
      <Button variant="text" onClick={addSortBy} data-name="add-sort-by">
        Add
      </Button>
    </div>
  );
};

export default SortByManager;
