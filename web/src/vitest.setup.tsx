import '@testing-library/jest-dom';
import { cleanup, configure } from '@testing-library/react';
import { afterEach, beforeEach, vi } from 'vitest';

vi.mock('@/firebase');

configure({
  getElementError: (message, _container) => {
    const error = new Error(message ?? '');
    error.name = 'TestingLibraryElementError';
    error.stack = undefined;
    return error;
  },
  asyncUtilTimeout: 5000,
});

beforeEach(() => {
  vi.useRealTimers();
});

afterEach(() => {
  cleanup();
});
