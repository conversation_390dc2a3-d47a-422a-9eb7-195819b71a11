import { auth, firestore } from '@/firebase';

const checkUser = () => {
  if (!auth?.currentUser) {
    throw new Error('No current user');
  }
};

const getImpersonationData = () => {
  // If doc key is uid, swap with impersonatedUser.uid
  const customLoginUser = localStorage.getItem('customLoginUser');
  const impersonatedUser = customLoginUser ? JSON.parse(customLoginUser) : null;

  return { impersonatedUser };
};

const Datastore = {
  addData: async <T extends { id: string }>(
    collection: string,
    data: T,
    validation?: (d: T) => boolean
  ) => {
    checkUser();
    if (validation instanceof Function && !validation(data)) {
      throw new Error('Invalid data');
    }

    const { impersonatedUser } = getImpersonationData();
    const user = impersonatedUser ?? auth.currentUser;
    if (data.id === auth?.currentUser?.uid) {
      data.id = user.uid;
    }
    const res = await firestore.collection(collection).add({
      ...data,
      uid: user.uid,
      state: 'active',
      created: Date.now(),
      updated: Date.now(),
    });
    return res.id;
  },

  addOrUpdateData: async <T extends { id: string }>(
    collection: string,
    data: T,
    validation?: (d: T) => boolean
  ) => {
    checkUser();
    if (validation instanceof Function && !validation(data)) {
      throw new Error('Invalid data');
    }
    const { impersonatedUser } = getImpersonationData();
    const user = impersonatedUser ?? auth.currentUser;
    if (data.id === auth?.currentUser?.uid) {
      data.id = user.uid;
    }
    const res = await firestore
      .collection(collection)
      .doc(data.id)
      .set(
        {
          ...data,
          uid: user.uid,
          state: 'active',
          created: Date.now(),
          updated: Date.now(),
        },
        { merge: true }
      );
    return res;
  },
  updateData: async <T extends { id?: string }>(
    collection: string,
    { id, ...data }: T
  ) => {
    checkUser();
    if (!id) {
      throw new Error('id required');
    }
    // If doc key is uid, swap with impersonatedUser.uid
    const { impersonatedUser } = getImpersonationData();
    const user = impersonatedUser ?? auth.currentUser;
    const docRef = firestore
      .collection(collection)
      .doc(id === auth?.currentUser?.uid ? user.uid : id);
    const doc = await docRef.get();
    if (!doc.exists) {
      throw new Error(`${id} not found.`);
    }
    await docRef.update({ ...data, updated: Date.now() });
  },
};

export default Datastore;
