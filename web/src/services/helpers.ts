import { SystemRoles } from 'common/globalTypes';
import {
  removeLeading<PERSON>railing<PERSON>har,
  tryDecodeURIComponent,
} from 'common/helpers';
import { saveAs } from 'file-saver';

import { LOCAL_STORAGE_KEYS } from '@/constants/account';
import { auth } from '@/firebase';
import API from '@/services/API';
import { serializeData } from '@/services/tools';
import { Roles } from '@/types';
import { getLocalData } from '@/utils/localStorage';
import { getEnvVariable } from '@/env';

export const impUser = (headers) => {
  const impUser = JSON.parse(
    localStorage.getItem('customLoginUser') ?? '{}'
  ).uid;
  if (impUser) {
    headers.impuid = impUser;
  }
  return headers;
};

export const addUid = (body) => {
  const impUser = JSON.parse(
    localStorage.getItem('customLoginUser') ?? '{}'
  ).uid;
  const selectedAccountId = getLocalData(
    LOCAL_STORAGE_KEYS.selectedAccount
  )?.accountId;

  if (impUser && !body.uid) {
    body.uid = impUser;
  } else if (!impUser && !body.uid) {
    body.uid = auth?.currentUser?.uid;
  }

  if (selectedAccountId && !body.accountId && !body.account_id) {
    body.account_id = selectedAccountId;
  }
  return body;
};

/**
 * Read file as array buffer
 * @param {File} file file
 * @returns {Promise<ArrayBuffer>} array buffer
 */
export const readFile = async (file) => {
  if (!file) {
    throw new Error('No file given');
  }
  const reader = new FileReader();
  return new Promise((resolve, reject) => {
    reader.onerror = () => {
      const error = 'Encountered error uploading document';
      reject(error);
    };
    reader.onload = () => {
      if (reader.result instanceof ArrayBuffer) {
        resolve(reader.result);
      }
    };
    reader.readAsArrayBuffer(file);
  });
};

export const exportCsv = (headers, data, filename) => {
  const csvLines = [];
  // Headers can be flat array of fieldIds or array of header objects
  const headerLabels = headers?.map((header) => {
    let headerLabel = header;
    if (typeof header === 'object') {
      if (header.getKey instanceof Function) {
        headerLabel = header.getKey();
      } else {
        headerLabel = header.id;
        if (header.id2) {
          headerLabel = header.id2;
        }
      }
    }
    return headerLabel;
  });
  // @ts-expect-error - this file needs to be typed properly --INITIAL_FIX--
  csvLines.push(headerLabels?.join(','));
  if (typeof data === 'string') {
    // @ts-expect-error - this file needs to be typed properly --INITIAL_FIX--
    csvLines.push(data.replace(',', ''));
  } else {
    for (const dataRow of data) {
      const csvLine = [];
      for (const [index, header] of headers.entries()) {
        let headerLabel = header;
        let aggregateKey: string | undefined;
        if (typeof header === 'object') {
          if (header.getKey instanceof Function) {
            headerLabel = header.getKey();
          } else {
            headerLabel = header.id;
            if (header.id2) {
              headerLabel = header.id2;
              aggregateKey = header.id;
            }
          }
        }
        let value = aggregateKey
          ? dataRow?.[aggregateKey]?.[headerLabel]?.[aggregateKey]
          : dataRow[headerLabel]
            ? dataRow[headerLabel]
            : dataRow[index];
        if (header.getter instanceof Function) {
          value = header.getter(value);
        }
        if (header.formatter instanceof Function) {
          value = header.formatter(value);
        }
        if (typeof value === 'string') {
          // TODO: Add more complete escaping
          value = value.replace(/\n/g, ' ');
          if (value.includes(',') || value.includes('"')) {
            value = `"${value}"`;
          }
        }
        // @ts-expect-error - this file needs to be typed properly --INITIAL_FIX--
        csvLine.push(value ?? '');
      }
      // @ts-expect-error - this file needs to be typed properly --INITIAL_FIX--
      csvLines.push(csvLine?.join(','));
    }
  }
  const csvText = csvLines?.join('\n');
  const element = document.createElement('a');
  element.setAttribute(
    'href',
    `data:text/plain;charset=utf-8,${encodeURIComponent(csvText)}`
  );
  element.setAttribute('download', filename);
  element.style.display = 'none';
  document.body.appendChild(element);
  element.click();
  document.body.removeChild(element);
};

/**
 * Download the reconciliations as csv
 * @param {{
 * page?: number|string;
 * limit?:number;
 * q?:string;
 * orderBy?:string | null;
 * sort?:string | null,
 * start?: Date | string;
 * end?: Date|string;
 * extraParams?: any;
 * }} data query options
 * @param {{idToken:string; endpoint: string | 'reconciliation_data' | 'statment_data' | 'report_data' | 'saved_reports' | 'custom_download'; exportOptions?: { fileName?: string, view?:string }}} option authentication token
 * @returns csv file data
 */
export const exportToCsv = async (data, option) => {
  const extraParams = data.extraParams;

  let _search = serializeData({
    q: data.q,
    orderBy: data.orderBy,
    sort: data.sort,
    endpoint: option.endpoint,
    exportOptions: JSON.stringify(option.exportOptions),
  });

  if (extraParams) {
    if (typeof extraParams === 'object') {
      for (const [key, value] of extraParams.entries()) {
        _search += `&${key}=${encodeURIComponent(value)}`;
      }
    } else if (typeof extraParams === 'string') {
      _search += extraParams;
    }
  }

  const res = await fetch(`${getEnvVariable('API')}/api/export?${_search}`, {
    method: 'GET',
    headers: await API.getHeaders(),
  });

  if (!res.ok) {
    return res.json().then((err) => {
      console.error('Error during export', err);
      throw new Error(err);
    });
  }

  try {
    await handleFileDownload(res, option);
  } catch (error) {
    console.error('Error during file download:', error);
    throw error;
  }
};

/**
 * Handle file download from response with proper content type detection and filename handling
 * @param {Response} res - The fetch response object
 * @param {object} option - Options object containing endpoint and exportOptions
 * @returns {Promise<void>}
 */
export const handleFileDownload = async (res, option) => {
  const contentType = res.headers.get('Content-Type');
  const _filename = res.headers
    .get('Content-Disposition')
    ?.split('filename=')?.[1];
  const filename = tryDecodeURIComponent(_filename || '');
  let extension: '.zip' | '.xlsx' | '.pdf' | '.csv';

  switch (contentType) {
    case 'application/zip':
      extension = '.zip';
      break;
    case 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':
      extension = '.xlsx';
      break;
    case 'application/pdf':
      extension = '.pdf';
      break;
    default:
      extension = '.csv';
  }

  const blob = await res.blob();
  let fileDownloadName = '';

  if (option?.exportOptions?.fileName) {
    fileDownloadName = `${option.exportOptions.fileName}${extension}`;
  } else if (filename) {
    fileDownloadName = removeLeadingTrailingChar(filename, '"');
  } else {
    fileDownloadName = `${option.endpoint}${extension}`;
  }

  saveAs(blob, fileDownloadName);
};

export const getMimeType = (fileName) => {
  const extension = fileName.split('.').pop().toLowerCase();
  switch (extension) {
    case 'xlsx':
    case 'xls':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'pdf':
      return 'application/pdf';
    case 'png':
      return 'image/png';
    case 'jpg':
    case 'jpeg':
      return 'image/jpeg';
    case 'csv':
      return 'text/csv';
    case 'txt':
      return 'text/plain';
    case 'html':
      return 'text/html';
    default:
      return 'application/octet-stream'; // Default to binary data
  }
};

export const convertFieldListToMapping = (fields) => {
  const mapping = {};
  fields.forEach((field, i) => {
    mapping[field] = i;
  });
  return mapping;
};

export const mapToArray = (obj) => {
  return Object.values(obj)?.map((item) => {
    const isObj = Object.prototype.toString.call(item) === '[object Object]';
    if (isObj) {
      // @ts-expect-error - this file needs to be typed properly --INITIAL_FIX--
      return Object.values(item);
    }
    return item;
  });
};

export const hasAccess = (acl, userRole, isFintaryAdmin) => {
  if (!acl) return true; // !acl = no restrictions added
  const _acl = Array.isArray(acl) ? acl : [acl];
  if (
    (_acl.includes(SystemRoles.ADMIN) || _acl.includes(Roles.FINTARY_ADMIN)) &&
    isFintaryAdmin
  )
    return true;
  return acl.includes(userRole);
};

export const requiresFintaryAdmin = (acl) => {
  const _acl = Array.isArray(acl) ? acl : [acl];
  return _acl.includes(SystemRoles.ADMIN) || _acl.includes(Roles.FINTARY_ADMIN);
};

/**
 * Check the input value type is the same as the type
 * @param {*} val
 * @param {String|Number|Boolean|Object|Date} _type
 */
export function checkType(val, _type) {
  // String, number, boolean, array, object, null, undefined, symbol, function Date, RegExp, Error, Promise, Map, Set, Math, JSON
  const type = Object.prototype.toString.call(val);
  return type === `[object ${_type}]`;
}

export const formatSearchDateParams = (dateString) => {
  if (!dateString || dateString === 'null') return null;
  else if (dateString === 'Invalid Date') return dateString;
  return new Date(dateString).toISOString().split('T')[0];
};

// TODO: When migrating to TypeScript, define the following types for filterHeadersForTable function
// headers: Header[],
// userRole: string,
// isAdmin: boolean
// return Header[]
export const filterHeadersForTable = (headers, userRole, isAdmin) => {
  return headers
    .filter((h) => hasAccess(h.access, userRole, isAdmin))
    .filter((h) => !Array.isArray(h.visible) || h.visible.includes('table'))
    .filter((h) => !['heading', 'divider'].includes(h.type));
};
