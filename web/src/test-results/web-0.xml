<?xml version="1.0" encoding="UTF-8" ?>
<testsuites name="vitest tests" tests="287" failures="0" errors="0" time="8.770268297">
    <testsuite name="hooks/useDynamicObjectMapUpdates.test.ts" timestamp="2025-08-29T01:35:45.583Z" hostname="Neils-MacBook-Air.local" tests="7" failures="0" errors="0" skipped="0" time="0.032911583">
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given a key is in skipUnmappedKeys, when addUpdatesTrack is called, should not add the track" time="0.014257667">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given no previous track, when addUpdatesTrack is called with &quot;added&quot;, should add the track as &quot;added&quot;" time="0.003284916">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given a previous track of &quot;added&quot;, when addUpdatesTrack is called with &quot;updated&quot;, should keep the track as &quot;added&quot;" time="0.003253042">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given a previous track of &quot;added&quot;, when addUpdatesTrack is called with &quot;deleted&quot;, should remove the track" time="0.001830333">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given a previous track of &quot;deleted&quot;, when addUpdatesTrack is called with &quot;added&quot;, should change the track to &quot;updated&quot;" time="0.002556334">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given a previous track of &quot;updated&quot;, when addUpdatesTrack is called with &quot;deleted&quot;, should change the track to &quot;deleted&quot;" time="0.002434459">
        </testcase>
        <testcase classname="hooks/useDynamicObjectMapUpdates.test.ts" name="useDynamicObjectMapUpdates &gt; addUpdatesTrack &gt; Given an objectMap without an updates property, when addUpdatesTrack is called, should create it and add the track" time="0.00312025">
        </testcase>
    </testsuite>
    <testsuite name="hooks/useExportOptions.test.ts" timestamp="2025-08-29T01:35:45.584Z" hostname="Neils-MacBook-Air.local" tests="3" failures="0" errors="0" skipped="0" time="0.041608917">
        <testcase classname="hooks/useExportOptions.test.ts" name="useExportOptions &gt; Given no processors, should return default options" time="0.022013084">
        </testcase>
        <testcase classname="hooks/useExportOptions.test.ts" name="useExportOptions &gt; Given valid processors, should return default and processor options" time="0.011363375">
        </testcase>
        <testcase classname="hooks/useExportOptions.test.ts" name="useExportOptions &gt; Given invalid processors, should filter out invalid processor options" time="0.006681958">
            <system-err>
Invalid processor data: { name: [32m&apos;&apos;[39m, str_id: [32m&apos;&apos;[39m, processor: [32m&apos;&apos;[39m }

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="utils/TestResultParser.test.ts" timestamp="2025-08-29T01:35:45.585Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.010393167">
        <testcase classname="utils/TestResultParser.test.ts" name="ParserFactory &gt; should return GeminiParser for AIModel.GEMINI" time="0.001495667">
        </testcase>
        <testcase classname="utils/TestResultParser.test.ts" name="ParserFactory &gt; should return DefaultParser for other models" time="0.000656">
        </testcase>
        <testcase classname="utils/TestResultParser.test.ts" name="TestResultParser &gt; should parse GEMINI result correctly" time="0.000933">
        </testcase>
        <testcase classname="utils/TestResultParser.test.ts" name="TestResultParser &gt; should parse default (non-GEMINI) result correctly" time="0.*********">
        </testcase>
        <testcase classname="utils/TestResultParser.test.ts" name="TestResultParser &gt; should return error object if result has error" time="0.*********">
        </testcase>
        <testcase classname="utils/TestResultParser.test.ts" name="TestResultParser &gt; should return [] if parse fails" time="0.*********">
            <system-err>
Error parsing default result: SyntaxError: Unexpected token &apos;o&apos;, &quot;not a json&quot; is not valid JSON
    at JSON.parse (&lt;anonymous&gt;)
    at DefaultParser.parse [90m(/Users/<USER>/Documents/repos/fintary/web/[39msrc/utils/TestResultParser.ts:44:19[90m)[39m
    at Function.parseResults [90m(/Users/<USER>/Documents/repos/fintary/web/[39msrc/utils/TestResultParser.ts:100:46[90m)[39m
    at [90m/Users/<USER>/Documents/repos/fintary/web/[39msrc/utils/TestResultParser.test.ts:110:37
    at file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:155:11
    at file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:752:26
    at file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1897:20
    at new Promise (&lt;anonymous&gt;)
    at runWithTimeout (file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1863:10)
    at runTest (file:///Users/<USER>/Documents/repos/fintary/node_modules/[4m@vitest[24m/runner/dist/chunk-hooks.js:1574:12)

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="components/DialogHost/DialogHost.test.tsx" timestamp="2025-08-29T01:35:45.585Z" hostname="Neils-MacBook-Air.local" tests="3" failures="0" errors="0" skipped="0" time="0.027255667">
        <testcase classname="components/DialogHost/DialogHost.test.tsx" name="DialogHost &gt; renders without crashing" time="0.013846875">
        </testcase>
        <testcase classname="components/DialogHost/DialogHost.test.tsx" name="DialogHost &gt; should not render any dialog if user is present" time="0.00670025">
        </testcase>
        <testcase classname="components/DialogHost/DialogHost.test.tsx" name="DialogHost &gt; should render email verification dialog if user is present and it&apos;s email is not verified" time="0.005421542">
        </testcase>
    </testsuite>
    <testsuite name="components/EmailVerificationDialog/EmailVerificationDialog.test.tsx" timestamp="2025-08-29T01:35:45.585Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="0" time="0.261553042">
        <testcase classname="components/EmailVerificationDialog/EmailVerificationDialog.test.tsx" name="SignUpDialog &gt; Rendering and Initial State &gt; renders without crashing" time="0.14437875">
        </testcase>
        <testcase classname="components/EmailVerificationDialog/EmailVerificationDialog.test.tsx" name="SignUpDialog &gt; Rendering and Initial State &gt; renders the email address" time="0.042806417">
        </testcase>
        <testcase classname="components/EmailVerificationDialog/EmailVerificationDialog.test.tsx" name="SignUpDialog &gt; Email Verification &gt; calls sendEmailVerification when button is clicked" time="0.042857041">
        </testcase>
        <testcase classname="components/EmailVerificationDialog/EmailVerificationDialog.test.tsx" name="SignUpDialog &gt; Email Verification &gt; displays snackbar message when email verification is sent" time="0.02982425">
        </testcase>
    </testsuite>
    <testsuite name="components/EmptyState/EmptyState.test.tsx" timestamp="2025-08-29T01:35:45.586Z" hostname="Neils-MacBook-Air.local" tests="1" failures="0" errors="0" skipped="0" time="0.027116417">
        <testcase classname="components/EmptyState/EmptyState.test.tsx" name="renders without crashing" time="0.025718375">
        </testcase>
    </testsuite>
    <testsuite name="components/ErrorBoundary/ErrorBoundary.test.tsx" timestamp="2025-08-29T01:35:45.586Z" hostname="Neils-MacBook-Air.local" tests="1" failures="0" errors="0" skipped="0" time="0.012454333">
        <testcase classname="components/ErrorBoundary/ErrorBoundary.test.tsx" name="renders without crashing" time="0.011550333">
        </testcase>
    </testsuite>
    <testsuite name="components/NotFoundPage/NotFoundPage.test.tsx" timestamp="2025-08-29T01:35:45.586Z" hostname="Neils-MacBook-Air.local" tests="1" failures="0" errors="0" skipped="0" time="0.029824">
        <testcase classname="components/NotFoundPage/NotFoundPage.test.tsx" name="renders without crashing" time="0.028717">
            <system-err>
⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="components/Loader/Loader.test.tsx" timestamp="2025-08-29T01:35:45.586Z" hostname="Neils-MacBook-Air.local" tests="1" failures="0" errors="0" skipped="0" time="0.047256667">
        <testcase classname="components/Loader/Loader.test.tsx" name="renders without crashing" time="0.04597925">
        </testcase>
    </testsuite>
    <testsuite name="components/SignInUp/SignInUp.test.tsx" timestamp="2025-08-29T01:35:45.586Z" hostname="Neils-MacBook-Air.local" tests="9" failures="0" errors="0" skipped="0" time="1.812231917">
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignIn View &gt; Given the component is rendered without query parameters, should display the sign in view by default" time="0.446998792">
            <system-err>
⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.

            </system-err>
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignIn View &gt; Given an email is provided as a query parameter, should pre-fill the email input field" time="0.*********">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignIn View &gt; Given the user is on the sign in view, when the &quot;Don&apos;t have an account?&quot; link is clicked, should switch to the sign up view" time="0.*********">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignIn View &gt; Given a valid email and password, when the sign in button is clicked, should call the signIn service and navigate on success" time="0.*********">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignUp View &gt; Given the loginType query parameter is &quot;signUp&quot;, should display the sign up view" time="0.*********">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignUp View &gt; Given an invalid invite code is submitted, should display an error message" time="0.*********">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignUp View &gt; Given a valid invite code is submitted, should display the full sign up form" time="0.146219833">
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignUp View &gt; Given the sign up form is submitted with valid data, should call the signUp service and display a success message" time="0.666715291">
            <system-out>
[36m&lt;body&gt;[39m
  [36m&lt;div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;MuiBox-root css-lx8lnm&quot;[39m
    [36m&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation3 css-1szhjxn-MuiPaper-root&quot;[39m
        [33mstyle[39m=[32m&quot;--Paper-shadow: 0px 3px 3px -2px rgba(0,0,0,0.2),0px 3px 4px 0px rgba(0,0,0,0.14),0px 1px 8px 0px rgba(0,0,0,0.12);&quot;[39m
      [36m&gt;[39m
        [36m&lt;div[39m
          [33mclass[39m=[32m&quot;MuiBox-root css-1fig7wu&quot;[39m
        [36m&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;MuiBox-root css-co4xce&quot;[39m
          [36m&gt;[39m
            [36m&lt;img[39m
              [33malt[39m=[32m&quot;Fintary Logo&quot;[39m
              [33mclass[39m=[32m&quot;MuiBox-root css-1wsuzl7&quot;[39m
              [33msrc[39m=[32m&quot;/logo192.png&quot;[39m
            [36m/&gt;[39m
            [36m&lt;h1[39m
              [33mclass[39m=[32m&quot;MuiTypography-root MuiTypography-h4 css-17nmnue-MuiTypography-root&quot;[39m
            [36m&gt;[39m
              [0mFintary[0m
            [36m&lt;/h1&gt;[39m
            [36m&lt;h6[39m
              [33mclass[39m=[32m&quot;MuiTypography-root MuiTypography-subtitle1 css-1a7pzqr-MuiTypography-root&quot;[39m
            [36m&gt;[39m
              [0mOne place for all your financial operations[0m
            [36m&lt;/h6&gt;[39m
          [36m&lt;/div&gt;[39m
          [36m&lt;div[39m
            [33mclass[39m=[32m&quot;MuiBox-root css-w7552a&quot;[39m
          [36m&gt;[39m
            [36m&lt;div[39m
              [33mclass[39m=[32m&quot;MuiBox-root css-9zybzw&quot;[39m
              [33mstyle[39m=[32m&quot;-webkit-transform: none; transform: none; transition: transform 225ms cubic-bezier(0.0, 0, 0.2, 1) 0ms;&quot;[39m
            [36m&gt;[39m
              [36m&lt;div[39m
                [33mclass[39m=[32m&quot;MuiBox-root css-1e8180r&quot;[39m
              [36m&gt;[39m
                [36m&lt;button[39m
                  [33mclass[39m=[32m&quot;MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-fullWidth MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeMedium MuiButton-outlinedSizeMedium MuiButton-colorPrimary MuiButton-fullWidth css-15e9p2l-MuiButtonBase-root-MuiButton-root&quot;[39m
                  [33mtabindex[39m=[32m&quot;0&quot;[39m
                  [33mtype[39m=[32m&quot;button&quot;[39m
                [36m&gt;[39m
                  [36m&lt;span[39m
                    [33mclass[39m=[32m&quot;MuiButton-icon MuiButton-startIcon MuiButton-iconSizeMedium css-1sh91j5-MuiButton-startIcon&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;svg[39m
                      [33maria-hidden[39m=[32m&quot;true&quot;[39m
                      [33mclass[39m=[32m&quot;MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root&quot;[39m
                      [33mdata-testid[39m=[32m&quot;GoogleIcon&quot;[39m
                      [33mfocusable[39m=[32m&quot;false&quot;[39m
                      [33mviewBox[39m=[32m&quot;0 0 24 24&quot;[39m
                    [36m&gt;[39m
                      [36m&lt;path[39m
                        [33md[39m=[32m&quot;M12.545,10.239v3.821h5.445c-0.712,2.315-2.647,3.972-5.445,3.972c-3.332,0-6.033-2.701-6.033-6.032s2.701-6.032,6.033-6.032c1.498,0,2.866,0.549,3.921,1.453l2.814-2.814C17.503,2.988,15.139,2,12.545,2C7.021,2,2.543,6.477,2.543,12s4.478,10,10.002,10c8.396,0,10.249-7.85,9.426-11.748L12.545,10.239z&quot;[39m
                      [36m/&gt;[39m
                    [36m&lt;/svg&gt;[39m
                  [36m&lt;/span&gt;[39m
                  [0mSign up with Google[0m
                [36m&lt;/button&gt;[39m
                [36m&lt;div[39m
                  [33maria-orientation[39m=[32m&quot;horizontal&quot;[39m
                  [33mclass[39m=[32m&quot;MuiDivider-root MuiDivider-fullWidth MuiDivider-withChildren css-7bs9ws-MuiDivider-root&quot;[39m
                  [33mrole[39m=[32m&quot;separator&quot;[39m
                [36m&gt;[39m
                  [36m&lt;span[39m
                    [33mclass[39m=[32m&quot;MuiDivider-wrapper css-1134oje-MuiDivider-wrapper&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;p[39m
                      [33mclass[39m=[32m&quot;MuiTypography-root MuiTypography-body2 css-1wle3ir-MuiTypography-root&quot;[39m
                    [36m&gt;[39m
                      [0mOr sign up with e-mail[0m
                    [36m&lt;/p&gt;[39m
                  [36m&lt;/span&gt;[39m
                [36m&lt;/div&gt;[39m
                [36m&lt;form[39m
                  [33mclass[39m=[32m&quot;MuiBox-root css-0&quot;[39m
                  [33mnovalidate[39m=[32m&quot;&quot;[39m
                [36m&gt;[39m
                  [36m&lt;div[39m
                    [33mclass[39m=[32m&quot;MuiFormControl-root MuiFormControl-marginDense MuiFormControl-fullWidth MuiTextField-root css-xanwcj-MuiFormControl-root-MuiTextField-root&quot;[39m
                    [33mdata-testid[39m=[32m&quot;email-address&quot;[39m
                  [36m&gt;[39m
                    [36m&lt;label[39m
                      [33mclass[39m=[32m&quot;MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-outlined MuiFormLabel-colorPrimary Mui-focused Mui-required MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-outlined css-113d811-MuiFormLabel-root-MuiInputLabel-root&quot;[39m
                      [33mdata-shrink[39m=[32m&quot;true&quot;[39m
                      [33mfor[39m=[32m&quot;emailAddress&quot;[39m
                      [33mid[39m=[32m&quot;emailAddress-label&quot;[39m
                    [36m&gt;[39m
                      [0mEmail address[0m
                      [36m&lt;span[39m
                        [33maria-hidden[39m=[32m&quot;true&quot;[39m
                        [33mclass[39m=[32m&quot;MuiFormLabel-asterisk MuiInputLabel-asterisk css-1ljffdk-MuiFormLabel-asterisk&quot;[39m
                      [36m&gt;[39m
                        [0m [0m
                        [0m*[0m
                      [36m&lt;/span&gt;[39m
                    [36m&lt;/label&gt;[39m
                    [36m&lt;div[39m
                      [33mclass[39m=[32m&quot;MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth Mui-focused MuiInputBase-formControl css-1blp12k-MuiInputBase-root-MuiOutlinedInput-root&quot;[39m
                    [36m&gt;[39m
                      [36m&lt;input[39m
                        [33maria-invalid[39m=[32m&quot;false&quot;[39m
                        [33mautocomplete[39m=[32m&quot;email&quot;[39m
                        [33mclass[39m=[32m&quot;MuiInputBase-input MuiOutlinedInput-input css-16wblaj-MuiInputBase-input-MuiOutlinedInput-input&quot;[39m
                        [33mid[39m=[32m&quot;emailAddress&quot;[39m
                        [33mname[39m=[32m&quot;emailAddress&quot;[39m
                        [33mrequired[39m=[32m&quot;&quot;[39m
                        [33mtype[39m=[32m&quot;text&quot;[39m
                        [33mvalue[39m=[32m&quot;&quot;[39...

            </system-out>
        </testcase>
        <testcase classname="components/SignInUp/SignInUp.test.tsx" name="SignInUp &gt; SignUp View &gt; Given the user is on the sign up view, when the &quot;Already have an account?&quot; link is clicked, should switch to the sign in view" time="0.*********">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/useDeleteAndCopyActions.test.ts" timestamp="2025-08-29T01:35:45.587Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.*********">
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given no flags enabled, should return base actions" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given create copy action is enabled, should add create copy action" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given delete action is enabled, should add delete action" time="0.0065795">
        </testcase>
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given create copy action is clicked, should call setDefaultData and setSearchParam" time="0.00970675">
        </testcase>
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given delete action is clicked, should set deleteIds and showDelConfirm" time="0.006129958">
        </testcase>
        <testcase classname="components/organisms/useDeleteAndCopyActions.test.ts" name="useDeleteAndCopyActions &gt; Given setShowDelConfirm and setDeleteIds are called, should update state correctly" time="0.001214083">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/ActionPicker.test.tsx" timestamp="2025-08-29T01:35:45.587Z" hostname="Neils-MacBook-Air.local" tests="9" failures="0" errors="0" skipped="0" time="0.00223375">
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given optionSelected is undefined, should return undefined" time="0.000593542">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given optionSelected is null, should return undefined" time="0.000145083">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of objects and optionSelected matches id, should return matching object" time="0.000253417">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of objects and optionSelected does not match any id, should return optionSelected as string" time="0.000115">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of primitives and optionSelected matches, should return optionSelected" time="0.000042958">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of primitives and optionSelected does not match, should return optionSelected" time="0.000049875">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is empty array, should return undefined if optionSelected is falsy" time="0.000037833">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of objects with numeric ids and optionSelected is a number, should return matching object" time="0.000102916">
        </testcase>
        <testcase classname="components/molecules/ActionPicker.test.tsx" name="getSelectedOption &gt; Given options is array of objects with numeric ids and optionSelected does not match, should return optionSelected" time="0.000246917">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/MultiSelect.test.tsx" timestamp="2025-08-29T01:35:45.587Z" hostname="Neils-MacBook-Air.local" tests="12" failures="0" errors="0" skipped="0" time="1.667573125">
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should render without crashing" time="0.080926417">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should render the label correctly" time="0.017234834">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should handle selection of items" time="0.222008084">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should filter items based on search query" time="0.104013834">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should handle &quot;All&quot; selection" time="0.045994959">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component &gt; should handle pagination" time="0.5156735">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should render without crashing" time="0.0068815">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should render the label correctly" time="0.031360708">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should handle selection of items" time="0.046768166">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should filter items based on search query" time="0.040497167">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should handle &quot;All&quot; selection" time="0.036005542">
        </testcase>
        <testcase classname="components/molecules/MultiSelect.test.tsx" name="MultiSelect Component with Object Values &gt; should handle pagination" time="0.518223083">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/MultiSelectV2.test.tsx" timestamp="2025-08-29T01:35:45.588Z" hostname="Neils-MacBook-Air.local" tests="13" failures="0" errors="0" skipped="0" time="1.460503833">
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; renders correctly with default props" time="0.234897292">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; displays string[] options correctly" time="0.148101958">
            <system-err>
Each child in a list should have a unique &quot;key&quot; prop.

Check the render method of `ForwardRef(Autocomplete)`. See https://react.dev/link/warning-keys for more information.

            </system-err>
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; displays object options correctly" time="0.121772291">
            <system-err>
Each child in a list should have a unique &quot;key&quot; prop.

Check the render method of `ul`. It was passed a child from ForwardRef(Autocomplete). See https://react.dev/link/warning-keys for more information.

            </system-err>
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles pagination correctly" time="0.375814417">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles pagination for object options correctly" time="0.148079208">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; displays loading state correctly" time="0.02226375">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles disabled prop correctly" time="0.010951042">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; renders custom options correctly" time="0.047264875">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles selection changes correctly" time="0.071420542">
            <system-err>
A props object containing a &quot;key&quot; prop is being spread into JSX:
  let props = {key: someKey, label: ..., className: ..., disabled: ..., data-tag-index: ..., tabIndex: ..., onDelete: ...};
  &lt;ForwardRef(Chip) {...props} /&gt;
React keys must be passed directly to JSX without using spread:
  let props = {label: ..., className: ..., disabled: ..., data-tag-index: ..., tabIndex: ..., onDelete: ...};
  &lt;ForwardRef(Chip) key={someKey} {...props} /&gt;

            </system-err>
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles selection changes for object options correctly" time="0.059891833">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles custom formatter and valuer for object options correctly" time="0.049231667">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; handles single selection mode correctly" time="0.084388084">
        </testcase>
        <testcase classname="components/molecules/MultiSelectV2.test.tsx" name="MultiSelectV2 Component &gt; calls renderCustomOption with selected value and available options" time="0.080014333">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/StringArrayFieldMatcher.test.tsx" timestamp="2025-08-29T01:35:45.588Z" hostname="Neils-MacBook-Air.local" tests="11" failures="0" errors="0" skipped="0" time="0.388987167">
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Renders correctly with empty initial values" time="0.11225775">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Enables Add button when input has value" time="0.022439708">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Adds a new value when clicking Add button" time="0.023628084">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Adds a new value when pressing Enter" time="0.024606">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Displays existing values as chips" time="0.019212833">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Deletes a value when chip delete button is clicked" time="0.012641041">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Parses array values correctly" time="0.017467166">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Handles empty strings properly" time="0.069453833">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Trims whitespace from added values" time="0.011903625">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Does not add empty values" time="0.025085917">
        </testcase>
        <testcase classname="components/molecules/StringArrayFieldMatcher.test.tsx" name="StringArrayFieldMatcher Component &gt; Appends new values to existing values" time="0.047163167">
        </testcase>
    </testsuite>
    <testsuite name="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" timestamp="2025-08-29T01:35:45.589Z" hostname="Neils-MacBook-Air.local" tests="7" failures="0" errors="0" skipped="0" time="0.036022">
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given useAdditionalActions is called, should return getAdditionalActions function" time="0.011554833">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given getAdditionalActions is called, should return actions with correct ids" time="0.005052417">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given statuses are checked, should enable &quot;reconcile&quot; action only for correct statuses" time="0.003804041">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given &quot;reconcile&quot; action is clicked, should call setSelectedStatment and setShowReconcile" time="0.003653833">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given &quot;update_policy_payout_rates&quot; action is clicked, should call mutateAsync and showSnackbar on success" time="0.00397075">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given mutateAsync fails, should show error snackbar" time="0.004679667">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useAdditionalActions.test.ts" name="useAdditionalActions &gt; Given mutateAsync fails with non-Error instance, should show error snackbar with stringified error" time="0.001605334">
        </testcase>
    </testsuite>
    <testsuite name="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" timestamp="2025-08-29T01:35:45.589Z" hostname="Neils-MacBook-Air.local" tests="16" failures="0" errors="0" skipped="0" time="0.070532458">
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given an agent with a corresponding config, then it should remove the agent, its config, and update the total" time="0.012816209">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given unit is rate, then it removes an agent and config and update total" time="0.003226667">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given config is undefined, then it should remove the agent and update the total" time="0.00299">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given updates is undefined, then it should remove the agent and initialize updates" time="0.001812916">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given existing updates, then it should add a new deleted update without modifying previous values" time="0.009752541">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; updateAgentData &gt; Given the new value is the same as the old one, then it should not update anything" time="0.003680459">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; updateAgentData &gt; Given unit is amount, then it should update the agent value, the total, and the updates tracker" time="0.01002625">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; updateAgentData &gt; Given unit is rate, then it should update the agent value, the total, and the updates tracker" time="0.001857375">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; updateAgentData &gt; Given an invalid new value, then it should update the agent value but not the total" time="0.012028583">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; updateAgentData &gt; Given existing updates, then it should add a new updated status without modifying previous values" time="0.002744709">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given an agent with a corresponding config, then it should remove the agent, its config, and update the total" time="0.004955583">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given unit is rate, then it removes an agent and config and update total" time="0.001013583">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given config is undefined, then it should remove the agent and update the total" time="0.000486875">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given updates is undefined, then it should remove the agent and initialize updates" time="0.000542166">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given existing updates, then it should add a new deleted update without modifying previous values" time="0.000398292">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCommissionsUpdates.test.ts" name="useCommissionsUpdates &gt; removeAgentData &gt; Given deleted data and receives new updates for same agent, then it should change the type from deleted to updated" time="0.000398167">
        </testcase>
    </testsuite>
    <testsuite name="components/CommissionsDataView/hooks/useCreatePolicy.test.ts" timestamp="2025-08-29T01:35:45.590Z" hostname="Neils-MacBook-Air.local" tests="2" failures="0" errors="0" skipped="0" time="0.021419958">
        <testcase classname="components/CommissionsDataView/hooks/useCreatePolicy.test.ts" name="useCreatePolicy &gt; Given handleCreatePolicy is called, should navigate with correct state" time="0.018470833">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useCreatePolicy.test.ts" name="useCreatePolicy &gt; Given commissionData has missing optional fields, should not throw" time="0.001790333">
        </testcase>
    </testsuite>
    <testsuite name="components/CommissionsDataView/hooks/useDataField.test.ts" timestamp="2025-08-29T01:35:45.590Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.048712584">
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; setNewFieldData &gt; given a simple field, should update the data row with the new value" time="0.032364792">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; setNewFieldData &gt; given a field that updates an object map &gt; should update the nested values correctly" time="0.003245333">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; setNewFieldData &gt; given a field that updates an object map &gt; should update and keep the existing metadata fields from nested values" time="0.002258375">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; getFieldData &gt; given a simple field, should return the direct value from the data row" time="0.002310375">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; getFieldData &gt; given a field that reads from an object map, should extract and return a primitive map" time="0.004696458">
        </testcase>
        <testcase classname="components/CommissionsDataView/hooks/useDataField.test.ts" name="useDataField &gt; getFieldData &gt; given data is not available for the field, should return an empty object" time="0.002289541">
        </testcase>
    </testsuite>
    <testsuite name="components/DataBulkAdd/hooks/useCompGridParse.test.ts" timestamp="2025-08-29T01:35:45.590Z" hostname="Neils-MacBook-Air.local" tests="7" failures="0" errors="0" skipped="0" time="0.*********">
        <testcase classname="components/DataBulkAdd/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; selectedParser &gt; should return the selected parser from search params" time="0.*********">
        </testcase>
        <testcase classname="components/DataBulkAdd/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; selectedParser &gt; should default to TransGlobal if account is TRANSGLOBAL and no parser is set" time="0.*********">
        </testcase>
        <testcase classname="components/DataBulkAdd/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; selectedParser &gt; should default to Fintary if account is not TRANSGLOBAL and no parser is set" time="0.*********">
        </testcase>
        <testcase classname="components/DataBulkAdd/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; availableParsers &gt; should return both Fintary and TransGlobal if account is TRANSGLOBAL" time="0.*********">
        </testcase>
        <testcase classname="components/DataBulkAdd/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; availableParsers &gt; should return only Fintary if account is not TRANSGLOBAL" time="0.*********">
        </testcase>
        <testcase classname="components/DataBulkAdd/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; handleSelectParser &gt; should set the parser in search params when called" time="0.*********">
        </testcase>
        <testcase classname="components/DataBulkAdd/hooks/useCompGridParse.test.ts" name="useCompGridParse &gt; handleSelectParser &gt; should remove the parser from search params if called with empty string" time="0.*********">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" timestamp="2025-08-29T01:35:45.591Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.*********">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given initial state, should reset state" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given qc param is missing, should do nothing" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given chip is not found, should do nothing" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given chip has addBtnLabel and hideAddSelect, should set them" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given add mode, should set new data and fields" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useAddGlobalCompanyButton.test.ts" name="useAddGlobalCompanyButton &gt; Given edit mode, should set fields" time="0.*********">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" timestamp="2025-08-29T01:35:45.591Z" hostname="Neils-MacBook-Air.local" tests="5" failures="0" errors="0" skipped="0" time="0.*********">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" name="useBuildFilterList &gt; Given filters and fieldOptions are undefined, should not call setFilterList" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" name="useBuildFilterList &gt; Given filters with _date_start and _date_end, should build filter list" time="0.*********">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" name="useBuildFilterList &gt; Given filters with multi-select array, should build filter list" time="0.004015958">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" name="useBuildFilterList &gt; Given filters is undefined, should build filter list from fieldOptions" time="0.001307417">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useBuildFilterList.test.ts" name="useBuildFilterList &gt; Given filters with empty arrays, should ignore them" time="0.004548541">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useDefaultSortingFromQuery.test.tsx" timestamp="2025-08-29T01:35:45.592Z" hostname="Neils-MacBook-Air.local" tests="8" failures="0" errors="0" skipped="0" time="0.06180275">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDefaultSortingFromQuery.test.tsx" name="useDefaultSortingFromQuery &gt; Given no query params, should return default sorting" time="0.012466584">
            <system-err>
⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDefaultSortingFromQuery.test.tsx" name="useDefaultSortingFromQuery &gt; Given order=asc and orderBy=name in query params, should return correct sorting" time="0.014638709">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDefaultSortingFromQuery.test.tsx" name="useDefaultSortingFromQuery &gt; Given order=desc and orderBy=amount in query params, should return correct sorting" time="0.004613084">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDefaultSortingFromQuery.test.tsx" name="useDefaultSortingFromQuery &gt; Given invalid order param, should default to DESC" time="0.002353916">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDefaultSortingFromQuery.test.tsx" name="useDefaultSortingFromQuery &gt; Given no orderBy param, should default orderBy to empty string" time="0.005000166">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDefaultSortingFromQuery.test.tsx" name="useUpdateSortingInQuery &gt; Given new sorting values, should update the query params" time="0.00881525">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDefaultSortingFromQuery.test.tsx" name="useUpdateSortingInQuery &gt; Given orderBy changes, should update the query params" time="0.008963292">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDefaultSortingFromQuery.test.tsx" name="useUpdateSortingInQuery &gt; Given orderBy not in columns, should remove orderBy from query" time="0.003412666">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useDisplayFields.test.ts" timestamp="2025-08-29T01:35:45.592Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="0" time="0.028888417">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDisplayFields.test.ts" name="useDisplayFields &gt; Given no saved fields in localStorage, should use defaultValue" time="0.011735042">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDisplayFields.test.ts" name="useDisplayFields &gt; Given saved fields in localStorage, should use them" time="0.003415917">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDisplayFields.test.ts" name="useDisplayFields &gt; Given localStorage returns empty string, should not use saved fields" time="0.00406675">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useDisplayFields.test.ts" name="useDisplayFields &gt; Given localStorage returns only commas, should not use saved fields" time="0.006932542">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useIsMobile.test.ts" timestamp="2025-08-29T01:35:45.592Z" hostname="Neils-MacBook-Air.local" tests="3" failures="0" errors="0" skipped="0" time="0.017836417">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useIsMobile.test.ts" name="useIsMobile &gt; Given media query matches, should return isMobile as true" time="0.010259875">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useIsMobile.test.ts" name="useIsMobile &gt; Given media query does not match, should return isMobile as false" time="0.00141975">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useIsMobile.test.ts" name="useIsMobile &gt; Given useMediaQuery is called, should use correct query" time="0.004664667">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useParams.test.ts" timestamp="2025-08-29T01:35:45.592Z" hostname="Neils-MacBook-Air.local" tests="5" failures="0" errors="0" skipped="0" time="0.041142875">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useParams.test.ts" name="useSearchParamsUrl &gt; Given searchParams and setSearchParams, should return them" time="0.017731167">
            <system-err>
⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useParams.test.ts" name="useSearchParamsUrl &gt; Given new params, should update searchParams" time="0.007070958">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useParams.test.ts" name="useSearchParamsUrl &gt; Given null or undefined params, should delete them" time="0.004506875">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useParams.test.ts" name="useSearchParamsUrl &gt; Given other params, should keep them unchanged" time="0.003640959">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useParams.test.ts" name="useSearchParamsUrl &gt; Given setSearchParams is called directly, should update searchParams" time="0.00673825">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/hooks/useSearchSettings.test.ts" timestamp="2025-08-29T01:35:45.593Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="0" time="0.036115334">
        <testcase classname="components/organisms/EnhancedDataView/hooks/useSearchSettings.test.ts" name="useSearchSettings &gt; Given table is report_data, should return correct settings" time="0.009187209">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useSearchSettings.test.ts" name="useSearchSettings &gt; Given table is reconciliation_data, should return correct settings" time="0.001615209">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useSearchSettings.test.ts" name="useSearchSettings &gt; Given table is statement_data, should return correct settings" time="0.016183917">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/hooks/useSearchSettings.test.ts" name="useSearchSettings &gt; Given table is unknown, should return empty array" time="0.00803675">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/EnhancedSelect/utils/search.test.ts" timestamp="2025-08-29T01:35:45.593Z" hostname="Neils-MacBook-Air.local" tests="10" failures="0" errors="0" skipped="0" time="0.006542125">
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; returns empty array if search is empty" time="0.001932583">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; performs quoted search (exact substring, case-insensitive)" time="0.000621208">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; performs quoted search with spaces" time="0.0003785">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; performs tokenize search with multiple tokens" time="0.000427458">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; performs tokenize search with comma and whitespace" time="0.000172833">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; performs simple substring search when tokenizeSearch is false" time="0.000234458">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; returns empty array if no match" time="0.000254375">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; handles case-insensitive search" time="0.000274">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; handles quoted search with only quotes" time="0.000549">
        </testcase>
        <testcase classname="components/molecules/EnhancedSelect/utils/search.test.ts" name="doSearch &gt; handles tokenizeSearch with empty tokens" time="0.000156167">
        </testcase>
    </testsuite>
    <testsuite name="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" timestamp="2025-08-29T01:35:45.593Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.55535675">
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given MoreDateFilters component, should render IconButton and title" time="0.088664083">
        </testcase>
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given IconButton click, should open Popover" time="0.160602834">
            <system-err>
React does not recognize the `endAdornment` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `endadornment` instead. If you accidentally passed it from a parent component, remove it from the DOM element.

            </system-err>
        </testcase>
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given value is &quot;true&quot;, should render checked Checkbox" time="0.065980875">
        </testcase>
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given Checkbox is toggled, should call onSetValue" time="0.112017125">
        </testcase>
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given missing checkbox filter, should handle gracefully" time="0.079424875">
            <system-err>
Filters are missing required fields:  {
  filters: [
    { filterKey: [32m&apos;startDate&apos;[39m, label: [32m&apos;start date&apos;[39m, type: [32m&apos;date&apos;[39m },
    { filterKey: [32m&apos;endDate&apos;[39m, label: [32m&apos;end date&apos;[39m, type: [32m&apos;date&apos;[39m }
  ]
}
Filters are missing required fields:  {
  filters: [
    { filterKey: [32m&apos;startDate&apos;[39m, label: [32m&apos;start date&apos;[39m, type: [32m&apos;date&apos;[39m },
    { filterKey: [32m&apos;endDate&apos;[39m, label: [32m&apos;end date&apos;[39m, type: [32m&apos;date&apos;[39m }
  ]
}

            </system-err>
        </testcase>
        <testcase classname="components/molecules/MoreDateFilters/components/MoreDateFilters.test.tsx" name="MoreDateFilters &gt; Given missing date fields, should handle gracefully" time="0.047210333">
            <system-err>
Filters are missing required fields:  {
  filters: [
    {
      filterKey: [32m&apos;includeBlanks&apos;[39m,
      label: [32m&apos;Include blanks&apos;[39m,
      type: [32m&apos;boolean&apos;[39m
    }
  ]
}
Filters are missing required fields:  {
  filters: [
    {
      filterKey: [32m&apos;includeBlanks&apos;[39m,
      label: [32m&apos;Include blanks&apos;[39m,
      type: [32m&apos;boolean&apos;[39m
    }
  ]
}

            </system-err>
        </testcase>
    </testsuite>
    <testsuite name="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" timestamp="2025-08-29T01:35:45.594Z" hostname="Neils-MacBook-Air.local" tests="11" failures="0" errors="0" skipped="0" time="0.*********">
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders green check and formatted amount when all amounts match" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders green check and formatted amount when total commission and statement match, bank is NaN" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders green check and formatted amount when total commission and bank match, statement is NaN" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders nothing when all amounts are NaN" time="0.004502">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders cross icon and all available amounts when they do not match" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders cross icon and both available amounts when statement and bank are present but total commission is missing" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders info icon and only available amount when only one amount is present" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders info icon and only available amount when only statement amount is present" time="0.********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders info icon and only available amount when only bank amount is present" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders statement amount for non-PROCESSED status" time="0.*********">
        </testcase>
        <testcase classname="components/documents/DocumentsView/components/ComissionTotalCell.test.tsx" name="CommissionTotalCell &gt; renders nothing for non-PROCESSED status and NaN statement amount" time="0.*********">
        </testcase>
    </testsuite>
    <testsuite name="components/contacts/ContactsView/ContactsTransactions/hooks/useTransactionUpdates.test.ts" timestamp="2025-08-29T01:35:45.594Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="4" time="0">
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/hooks/useTransactionUpdates.test.ts" name="useTransactionUpdates &gt; when updateTransactions is called &gt; Given new transactions are added, should update transactions and pagination correctly" time="0">
            <skipped/>
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/hooks/useTransactionUpdates.test.ts" name="useTransactionUpdates &gt; when updateTransactions is called &gt; Given transactions are removed, should update transactions and pagination correctly" time="0">
            <skipped/>
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/hooks/useTransactionUpdates.test.ts" name="useTransactionUpdates &gt; when updateTransactions is called &gt; Given all transactions are removed, should update transactions and pagination correctly" time="0">
            <skipped/>
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/hooks/useTransactionUpdates.test.ts" name="useTransactionUpdates &gt; when updateTransactions is called &gt; Given the number of transactions is unchanged, should update transactions but keep totalItems the same" time="0">
            <skipped/>
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" timestamp="2025-08-29T01:35:45.594Z" hostname="Neils-MacBook-Air.local" tests="8" failures="0" errors="0" skipped="0" time="0.674720917">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given bookmark icon button, should render it" time="0.075946167">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given params exist, should show tooltip with correct message" time="0.209704041">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given params are empty, should show tooltip with correct message" time="0.037130375">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given popover is opened, should allow entering view name" time="0.084918583">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given onSave is called, should show snackbar on success" time="0.110428292">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given onSave is called, should show snackbar on error" time="0.085588834">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given table prop, should compute correct page" time="0.031969542">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Bookmark.test.tsx" name="Bookmark &gt; Given unknown table, should compute empty page" time="0.036032292">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/FilterDateRange.test.tsx" timestamp="2025-08-29T01:35:45.595Z" hostname="Neils-MacBook-Air.local" tests="4" failures="0" errors="0" skipped="0" time="0.048923542">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterDateRange.test.tsx" name="FilterDateRange &gt; Given date range picker, should render it" time="0.026186916">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterDateRange.test.tsx" name="FilterDateRange &gt; Given dateFilters are provided, should render MoreDateFilters" time="0.011978416">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterDateRange.test.tsx" name="FilterDateRange &gt; Given isMobile is true, should render with mobile styles" time="0.006014042">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterDateRange.test.tsx" name="FilterDateRange &gt; Given empty dateFilters, should not render MoreDateFilters" time="0.0032125">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" timestamp="2025-08-29T01:35:45.595Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.265456542">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given no chips, should render nothing" time="0.015487833">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given chips, should render all visible chips" time="0.077159541">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given admin chip and user is admin, should show admin chip with lock" time="0.115428">
            <system-out>
[36m&lt;body&gt;[39m
  [36m&lt;div&gt;[39m
    [36m&lt;div[39m
      [33mclass[39m=[32m&quot;MuiBox-root css-i44mvr&quot;[39m
    [36m&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;MuiButtonBase-root MuiChip-root MuiChip-filled MuiChip-sizeMedium MuiChip-colorPrimary MuiChip-clickable MuiChip-clickableColorPrimary MuiChip-filledPrimary css-17ql1vv-MuiButtonBase-root-MuiChip-root&quot;[39m
        [33mrole[39m=[32m&quot;button&quot;[39m
        [33mtabindex[39m=[32m&quot;0&quot;[39m
      [36m&gt;[39m
        [36m&lt;span[39m
          [33mclass[39m=[32m&quot;MuiChip-label MuiChip-labelMedium css-1dybbl5-MuiChip-label&quot;[39m
        [36m&gt;[39m
          [0mAll[0m
        [36m&lt;/span&gt;[39m
      [36m&lt;/div&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeMedium MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-5rfr0w-MuiButtonBase-root-MuiChip-root&quot;[39m
        [33mdata-testid[39m=[32m&quot;foo&quot;[39m
        [33mrole[39m=[32m&quot;button&quot;[39m
        [33mtabindex[39m=[32m&quot;0&quot;[39m
      [36m&gt;[39m
        [36m&lt;span[39m
          [33mclass[39m=[32m&quot;MuiChip-label MuiChip-labelMedium css-16cgrcw-MuiChip-label&quot;[39m
        [36m&gt;[39m
          [0mFoo[0m
        [36m&lt;/span&gt;[39m
      [36m&lt;/div&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeMedium MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-5rfr0w-MuiButtonBase-root-MuiChip-root&quot;[39m
        [33mrole[39m=[32m&quot;button&quot;[39m
        [33mtabindex[39m=[32m&quot;0&quot;[39m
      [36m&gt;[39m
        [36m&lt;span[39m
          [33mclass[39m=[32m&quot;MuiChip-label MuiChip-labelMedium css-16cgrcw-MuiChip-label&quot;[39m
        [36m&gt;[39m
          [0mBar[0m
        [36m&lt;/span&gt;[39m
      [36m&lt;/div&gt;[39m
      [36m&lt;div[39m
        [33mclass[39m=[32m&quot;MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeMedium MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-5rfr0w-MuiButtonBase-root-MuiChip-root&quot;[39m
        [33mrole[39m=[32m&quot;button&quot;[39m
        [33mtabindex[39m=[32m&quot;0&quot;[39m
      [36m&gt;[39m
        [36m&lt;span[39m
          [33mclass[39m=[32m&quot;MuiChip-label MuiChip-labelMedium css-16cgrcw-MuiChip-label&quot;[39m
        [36m&gt;[39m
          [0mAdmin 🔒[0m
        [36m&lt;/span&gt;[39m
      [36m&lt;/div&gt;[39m
    [36m&lt;/div&gt;[39m
  [36m&lt;/div&gt;[39m
[36m&lt;/body&gt;[39m

            </system-out>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given admin chip and user is not admin, should not show admin chip" time="0.008738917">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given chip with more=true, should render MoreMenu" time="0.038179875">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterQueryChips.test.tsx" name="FilterQueryChips &gt; Given chip is clicked, should call setSearchParams" time="0.007194917">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" timestamp="2025-08-29T01:35:45.595Z" hostname="Neils-MacBook-Air.local" tests="5" failures="0" errors="0" skipped="0" time="0.273576834">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" name="FilterSelect &gt; Given filters with object and string options, should render filter selects" time="0.060429042">
            <system-err>
React does not recognize the `enableActiveColor` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `enableactivecolor` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `enableSearch` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `enablesearch` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `tokenizeSearch` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `tokenizesearch` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `listContainerSx` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `listcontainersx` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `renderLabel` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `renderlabel` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `renderValue` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `rendervalue` instead. If you accidentally passed it from a parent component, remove it from the DOM element.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" name="FilterSelect &gt; Given filters and params exist, should show reset button" time="0.118220375">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" name="FilterSelect &gt; Given clear filters button is clicked, should call setSearchParams" time="0.082971875">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" name="FilterSelect &gt; Given default sorting, should sort filters by label" time="0.005816">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/FilterSelect.test.tsx" name="FilterSelect &gt; Given sortFilterByPosition is enabled, should sort filters by sortPosition" time="0.004522208">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" timestamp="2025-08-29T01:35:45.595Z" hostname="Neils-MacBook-Air.local" tests="6" failures="0" errors="0" skipped="0" time="0.117510875">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given FilterQueryChips, FilterDateRange, and EnhancedDataViewFilterSelect, should render them" time="0.037281667">
            <system-err>
⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given FilterNumberRange, should render it when numberRangeFilters provided" time="0.012177792">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given navigation icons, should render left and right icons" time="0.012940292">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given navigation icons are clicked, should call scrollFilter" time="0.03558675">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given enableResetFilters and sortFilterByPosition, should pass them to EnhancedDataViewFilterSelect" time="0.008036333">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/Filters.test.tsx" name="Filters &gt; Given scroll event, should call setIsFilterScrollable" time="0.009941">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" timestamp="2025-08-29T01:35:45.596Z" hostname="Neils-MacBook-Air.local" tests="11" failures="0" errors="0" skipped="0" time="0.270218042">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given default state, should render fields select and add button" time="0.116546792">
            <system-err>
The `value` prop supplied to &lt;select&gt; must be an array if `multiple` is true.

Check the render method of `EnhancedSelect`.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given hideAddSelect is true, should not render add button" time="0.017333">
            <system-err>
The `value` prop supplied to &lt;select&gt; must be an array if `multiple` is true.

Check the render method of `EnhancedSelect`.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given default state, should render export button" time="0.005807625">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given enableSaves is true, should render bookmark" time="0.002330958">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given extraActions of type button, should render as button" time="0.034613208">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given extraActions of type select, should render as select" time="0.04480325">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given extraActions of type date, should render as date" time="0.008455125">
            <system-err>
React does not recognize the `setValue` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `setvalue` instead. If you accidentally passed it from a parent component, remove it from the DOM element.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given extraActions of type dateRange, should render as dateRange" time="0.007099709">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given extraActions of type multiSelect, should render as multiSelect" time="0.013179">
            <system-err>
React does not recognize the `selectedValues` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `selectedvalues` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
React does not recognize the `setSelectedValues` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `setselectedvalues` instead. If you accidentally passed it from a parent component, remove it from the DOM element.

            </system-err>
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given hideExport is true, should not render export button" time="0.011143292">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/RightActions.test.tsx" name="RightActions &gt; Given fields select changes, should call setFieldsStorage" time="0.006311666">
        </testcase>
    </testsuite>
    <testsuite name="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" timestamp="2025-08-29T01:35:45.596Z" hostname="Neils-MacBook-Air.local" tests="5" failures="0" errors="0" skipped="0" time="0.075421709">
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" name="TitleAndSearchBox &gt; Given default props, should render label and SearchBox" time="0.029972875">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" name="TitleAndSearchBox &gt; Given tabbed variant, should not render label" time="0.005997792">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" name="TitleAndSearchBox &gt; Given searchSettings is not empty, should render SearchSettings" time="0.004735583">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" name="TitleAndSearchBox &gt; Given isMobile true, should set flexWrap to wrap" time="0.028787084">
        </testcase>
        <testcase classname="components/organisms/EnhancedDataView/components/Toolbar/TitleAndSearchBox.test.tsx" name="TitleAndSearchBox &gt; Given isMobile false, should set flexWrap to nowrap" time="0.004478833">
        </testcase>
    </testsuite>
    <testsuite name="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" timestamp="2025-08-29T01:35:45.596Z" hostname="Neils-MacBook-Air.local" tests="17" failures="0" errors="0" skipped="0" time="0.005738875">
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; should parse the input to a valid amount correctly for &apos;0.45&apos;" time="0.001708167">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; should parse the input to a valid amount correctly for &apos;10.999&apos;" time="0.000390458">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; should parse the input to a valid amount correctly for &apos;50.05954&apos;" time="0.000113833">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; should parse the input to a valid amount correctly for &apos;12005&apos;" time="0.000149458">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: abc" time="0.0002365">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 123..45" time="0.000093334">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: --123" time="0.000053333">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 12.34.56" time="0.000095666">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 12.00.00" time="0.000308666">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 12a3" time="0.000267584">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 1,23,4.56" time="0.0001825">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: 12,34" time="0.000085708">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: ." time="0.000213541">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: -" time="0.00008075">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs:  " time="0.000099125">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: null" time="0.000054709">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/amountCell.helper.test.ts" name="Given parseStringInputToValidAmount, when parsing input, should handle various cases &gt; invalid inputs &gt; should return expect result when received invalid inputs: undefined" time="0.000052167">
        </testcase>
    </testsuite>
    <testsuite name="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" timestamp="2025-08-29T01:35:45.597Z" hostname="Neils-MacBook-Air.local" tests="34" failures="0" errors="0" skipped="1" time="0.0082665">
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionDetail &gt; Should remove the transaction detail for the found transaction in the transactions list by target input" time="0.002288083">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionDetail &gt; Should not change the transactions input when the transaction or transaction detail is not found" time="0.000458666">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionDetail &gt; Should handle cases where the transaction exists but the detail does not" time="0.000131667">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionDetail &gt; Should handle cases where the transaction has no details" time="0.00008175">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionDetail &gt; Should return a new transactions array and not mutate the original input" time="0.000627833">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionById &gt; Should remove the transaction with the given ID from the transactions list" time="0.000177">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionById &gt; Should not modify the transactions list if the transaction ID is not found" time="0.000069834">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionById &gt; Should return a new transactions array and not mutate the original input" time="0.000140916">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="deleteTransactionById &gt; Should handle an empty transactions list gracefully" time="0.000182333">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should return updated and deleted transactions based on updatedRows and removedRows" time="0.000309667">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should return only updated transactions when no rows are removed" time="0.000176375">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should return only deleted transactions when no rows are updated" time="0.000092125">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should return empty arrays when there are no updated or removed rows" time="0.000063">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should handle cases where updatedRows reference non-existent transactions" time="0.000057667">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getTransactionsUpdates &gt; Should handle cases where removedRows reference non-existent transactions" time="0">
            <skipped/>
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getNonSettledTransactions &gt; Should return transactions that are not settled" time="0.000122333">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getNonSettledTransactions &gt; Should return an empty array if all transactions are settled" time="0.000047333">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getNonSettledTransactions &gt; Should return transactions with filtered details if some details are settled" time="0.00019675">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getNonSettledTransactions &gt; Should handle an empty transactions list gracefully" time="0.000039291">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="getNonSettledTransactions &gt; Should return transactions unchanged if none are settled" time="0.000052625">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="addNewTransactionDetail &gt; Should add a new transaction detail to the specified transaction" time="0.000079667">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="addNewTransactionDetail &gt; Should not modify transactions if the transaction ID is not found" time="0.000043792">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="addNewTransactionDetail &gt; Should return a new transactions array and not mutate the original input" time="0.0001115">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="addNewTransactionDetail &gt; Should handle an empty transactions list gracefully" time="0.000038375">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should update the specified transaction detail in the given transaction" time="0.000257792">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should update the transaction amout with a sum of transaction details amount" time="0.000227875">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should not modify transactions if the transaction ID is not found" time="0.0000525">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should not modify transactions if the transaction detail ID is not found" time="0.00006025">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should return a new transactions array and not mutate the original input" time="0.000075">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransactionDetails &gt; Should handle an empty transactions list gracefully" time="0.00004725">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransaction &gt; Should update the specified transaction with the provided updates" time="0.000086875">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransaction &gt; Should not modify transactions if the transaction ID is not found" time="0.00005">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransaction &gt; Should return a new transactions array and not mutate the original input" time="0.000065958">
        </testcase>
        <testcase classname="components/contacts/ContactsView/ContactsTransactions/components/helpers/transactionTable.helpers.test.ts" name="updateTransaction &gt; Should handle an empty transactions list gracefully" time="0.00004375">
        </testcase>
    </testsuite>
</testsuites>
