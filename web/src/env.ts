import { captureException } from '@sentry/react';

const envConfig = {
  NAME: () => '',
  VERSION: () => '',
  DESCRIPTION: () => '',
  HOMEPAGE: () => '',
  TITLE: () => '',
  THEME_PRIMARY_COLOR: () => '',
  THEME_SECONDARY_COLOR: () => '',
  THEME_DARK: () => '',
  SENTRY_DSN: () => '',
  API: () => '',
  AI: () => '',
  FIREBASE_API_KEY: () => '',
  FIREBASE_AUTH_DOMAIN: () => '',
  FIREBASE_PROJECT_ID: () => '',
  FIREBASE_STORAGE_BUCKET: () => '',
  FIREBASE_MESSAGING_SENDER_ID: () => '',
  FIREBASE_APP_ID: () => '',
  FIREBASE_MEASUREMENT_ID: () => '',
  STATSIG_API_KEY: () => '',
  ENVIRONMENT: () => '',
  USE_FIREBASE_EMULATOR: () => '',
  VITEST: () => '',
  NODE_ENV: () => '',
  BASENAME: () => '',
} as const;

for (const key of Object.keys(envConfig)) {
  envConfig[key] = () =>
    import.meta.env[`VITE_${key}`] ??
    import.meta.env[`REACT_APP_${key}`] ??
    import.meta.env[key];
}

export const isTestMode = () => envConfig.VITEST() === 'true';
export const isLocalApp = () => envConfig.API() === 'http://localhost:3001';
export const isDevApp = () =>
  isLocalApp() || envConfig.API() === 'https://api-dev.fintary.com';
export const isProductionApp = () =>
  envConfig.FIREBASE_PROJECT_ID() === 'fintary-prod' ||
  (envConfig.API() === 'https://api.fintary.com' &&
    envConfig.ENVIRONMENT() === 'production');

type EnvVariable = keyof typeof envConfig;

type GetEnvVariable =
  | EnvVariable
  | {
      name: EnvVariable;
      defaultValue?: string;
      shouldThrow?: boolean;
      failSilently?: boolean;
    };

export const getEnvVariable = (envVar: GetEnvVariable) => {
  const name = typeof envVar === 'string' ? envVar : envVar.name;
  const shouldThrow = typeof envVar !== 'string' && envVar.shouldThrow;
  const defaultValue =
    typeof envVar !== 'string' ? envVar.defaultValue : undefined;
  const failSilently = typeof envVar !== 'string' && envVar.failSilently;
  const value = envConfig[name]();
  if (value == null) {
    if (defaultValue != null) {
      return defaultValue;
    }
    const errorMessage = `Missing environment variable: ${name}`;
    if (!isProductionApp() && !isTestMode() && !failSilently && !defaultValue) {
      console.error(errorMessage);
      console.trace(name);
    }
    if (isProductionApp() && !failSilently && !defaultValue) {
      console.error(errorMessage);
      captureException(errorMessage);
    }
    if (shouldThrow) {
      throw new Error(errorMessage);
    }
    return '';
  }
  return value;
};
