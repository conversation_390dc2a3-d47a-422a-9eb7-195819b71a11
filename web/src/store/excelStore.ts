import { create } from 'zustand';

type ExcelState = {
  // biome-ignore lint/suspicious/noExplicitAny: This is either File or File[]. It's used differently throughout the app.
  overrideFile: any | null;
  // biome-ignore lint/suspicious/noExplicitAny: This is either File or File[]. It's used differently throughout the app.
  originFile: any | null;
  // biome-ignore lint/suspicious/noExplicitAny: This is either File or File[]. It's used differently throughout the app.
  uploadedFile: any | null;
  rawData: unknown[];
  rawDataSheets: unknown[];
  processedData: unknown;
  setOriginFile: (file: ExcelState['overrideFile']) => void;
  setOverrideFile: (file: ExcelState['originFile']) => void;
  setUploadedFile: (file: ExcelState['uploadedFile']) => void;
  setRawData: (file: ExcelState['rawData']) => void;
  setRawDataSheets: (file: ExcelState['rawDataSheets']) => void;
  setProcessedData: (file: ExcelState['processedData']) => void;
  updateValueInRawData: (
    index: PropertyKey,
    attr: unknown,
    newValue: unknown
  ) => void;
};

const useExcelStore = create<ExcelState>((set) => ({
  overrideFile: null,
  // The file uploaded by user
  originFile: null,

  uploadedFile: null,
  // The raw data from the file
  rawData: [],
  rawDataSheets: [],
  // The processed data from the raw data
  processedData: [],

  setOriginFile: (file) => set({ originFile: file }),

  setOverrideFile: (file) => set({ overrideFile: file }),

  setUploadedFile: (file) => set({ uploadedFile: file }),

  setRawData: (data) => set({ rawData: data }),
  setRawDataSheets: (sheets) => set({ rawDataSheets: sheets }),
  setProcessedData: (data) => set({ processedData: data }),
  updateValueInRawData: (index, attr, newValue) =>
    set((state) => {
      const newData = [...state.rawData];
      newData[index][attr] = newValue;
      return { rawData: newData };
    }),
}));

export const useOriginalFile = () => useExcelStore((state) => state.originFile);
export const useOverrideFile = () =>
  useExcelStore((state) => state.overrideFile);

export const useUploadedFile = () =>
  useExcelStore((state) => state.uploadedFile);

export const useRawData = () => useExcelStore((state) => state.rawData);
export const useRawDataSheets = () =>
  useExcelStore((state) => state.rawDataSheets);
export const useProcessedData = () =>
  useExcelStore((state) => state.processedData);

export const useSetOriginFile = () =>
  useExcelStore((state) => state.setOriginFile);

export const useSetOverrideFile = () =>
  useExcelStore((state) => state.setOverrideFile);

export const useSetUploadedFile = () =>
  useExcelStore((state) => state.setUploadedFile);

export default useExcelStore;
